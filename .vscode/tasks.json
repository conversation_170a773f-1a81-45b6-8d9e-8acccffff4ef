{
    "version": "2.0.0",
    "tasks": [
        {
            "type": "dart",
            "command": "dart",
            "args": [
                "run",
                "build_runner",
                "build"
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "label": "Embark: run code generators",
            "detail": ""
        },
        {
            "type": "dart",
            "command": "dart",
            "args": [
                "run",
                "build_runner",
                "build",
                "--delete-conflicting-outputs"
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "label": "flutter: flutter pub run build_runner build --delete-conflicting-outputs",
            "detail": ""
        },
        {
            "type": "flutter",
            "command": "flutter",
            "args": [
                "gen-l10n",
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "label": "flutter: gen-l10n",
            "detail": ""
        },
        {
            "type": "dart",
            "command": "dart",
            "cwd": "packages/bi_embark_l10n",
            "args": [
                "run",
                "bi_flutter_l10n_gen",
            ],
            "problemMatcher": [
                "$dart-build_runner"
            ],
            "group": {
                "kind": "build",
                "isDefault": false
            },
            "label": "Embark: generate translations",
            "detail": "Downloads and applies translations from Google Translate."
        },
        {
            "type": "shell",
            "command": "dart",
            "args": [
                "run",
                "bi_flutter_ui_test",
                "coverage",
            ],
            "label": "Embark: Check CI Code Coverage",
            "detail": "Runs the CI commands to ensure local code meets CI code coverage requirements.",
            "problemMatcher": [],
        },
        {
            "type": "shell",
            "command": "dart",
            "args": [
                "run",
                "bi_flutter_ui_test",
                "coverage",
                "--no-build"
            ],
            "label": "Embark: Check CI Code Coverage (without cleaning)",
            "detail": "Runs the CI commands to ensure local code meets CI code coverage requirements without doing a flutter clean first.",
            "problemMatcher": [],
        }
    ]
}
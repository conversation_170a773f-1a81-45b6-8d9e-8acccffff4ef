{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "embark (android)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--flavor",
                "dev"
            ],
            "templateFor": "lib/main.dart",
        },
        {
            "name": "embark (iOS)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
        },
        {
            "name": "embark (android - no debug)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "args": [
                "--flavor",
                "dev"
            ],
            "templateFor": "lib/main.dart",
            "noDebug": true
        },
        {
            "name": "embark (iOS - no debug)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main.dart",
            "noDebug": true
        },
        {
            "name": "embark (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "embark (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "authentication_repository",
            "cwd": "packages\\authentication_repository",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "user_repository",
            "cwd": "packages\\user_repository",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "widgets",
            "cwd": "packages\\widgets",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "widgets (profile mode)",
            "cwd": "packages\\widgets",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "widgets (release mode)",
            "cwd": "packages\\widgets",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "Golden",
            "request": "launch",
            "type": "dart",
            "codeLens": {
                "for": [
                    "run-test",
                    "run-test-file"
                ]
            },
            "args": [
                "--update-goldens"
            ]
        }
    ]
}
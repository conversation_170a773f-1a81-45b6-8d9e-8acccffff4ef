import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/services/background_jobs/auth/_background_job_credential_service.dart';
import 'package:embark/services/services.dart';

const String _isReviewUserKey = 'isReviewUser';

/// A helper method to just check if the review user flag is set or not.
bool isReviewUser(SharedPreferencesService sharedPreferences) =>
    sharedPreferences.getBool(_isReviewUserKey, false);

/// <PERSON><PERSON> intercepting the STS login requests and switching CDN's
/// as necessary to support the review users when app stores are reviewing.
class BiEmbarkStsHttpClient extends BiStsHttpClient {
  @override
  bool get persistPassword => false;

  final String reviewUserEmail;
  final SharedPreferencesService sharedPreferences;
  final BackgroundJobCredentialService backgroundJobCredentialService;

  BiEmbarkStsHttpClient({
    required super.dio,
    required super.logger,
    required super.clientId,
    required super.clientSecret,
    required this.reviewUserEmail,
    required this.sharedPreferences,
    required this.backgroundJobCredentialService,
  });

  @override
  Future<StsLoginResponse> login(LoginCredentials? loginCredentials) async {
    final wasReviewUser = isReviewUser(sharedPreferences);
    final username = loginCredentials?.username;

    if (username == reviewUserEmail) {
      // save is reviewer flag
      await sharedPreferences.setBool(_isReviewUserKey, true);
    } else {
      // not review user
      await sharedPreferences.setBool(_isReviewUserKey, false);
    }

    if (isReviewUser(sharedPreferences) != wasReviewUser) {
      // changed either to/from review user, so clear the cache
      final interceptor = dio.interceptors
              .firstWhere((i) => i is BiDomainUrlResolverInterceptor)
          as BiDomainUrlResolverInterceptor;
      // clear the cache since we need to update domain lists.
      interceptor.resetCache();
    }

    final response = await super.login(loginCredentials);

    if (loginCredentials != null && response.accessToken.isNotEmpty) {
      // logged in with username + password success, so store these credentials for
      // background jobs
      await backgroundJobCredentialService.setCredentials(loginCredentials);
    }

    return response;
  }
}

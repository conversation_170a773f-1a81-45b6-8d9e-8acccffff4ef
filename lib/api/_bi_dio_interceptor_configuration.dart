import 'dart:io';

import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_login_widget/authentication/models/user.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/api/_bi_embark_sts_http_client.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/background_jobs/auth/_background_job_bearer_header_interceptor.dart';
import 'package:embark/services/background_jobs/auth/_background_job_credential_service.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';

/// Provides static methods for configuring or updating interceptors
/// for a Dio instance.
class BiDioInterceptorConfiguration {
  static void apply({
    required AppId app,
    required String appVersion,
    required BiCdnHttpClient primaryCdn,
    required BiCdnHttpClient secondaryCdn,
    required BiTokenManager tokenManager,
    required Dio dio,
    required BiLogger logger,
    required AppRepository appRepository,
    required InternetConnectivityRepository internetConnectivityRepo,
    required EmbarkAuthenticationRepository embarkAuthenticationRepository,
    required SharedPreferencesService sharedPreferences,
    required DeviceSetupRepository deviceSetupRepository,
  }) {
    // reset all interceptors
    dio.interceptors.clear();

    // set the content type to be JSON, so that way all requests
    // get the 'Content-Type: application/json; charset: utf-8
    // This also makes it so that the SMLK Dio clients can
    // pass just a Map<String, dynamic> to any requests for payloads
    // and Dio will handle auto-converting that into a JSON string.
    dio.options.contentType = ContentType.json.value;

    // Very first thing is to set the current app id. This
    // can be changed later (for in the case we determine we are on BIM).
    // The CDN interceptor will use this ID to resolve domains.
    dio.options.biAppId = app;

    dio.interceptors.addAll([
      // log all requests
      BiRequestLoggingInterceptor(logger: logger),

      // terminate requests if no internet.
      _buildInternetRequiredInterceptor(
        logger: logger,
        internetConnectivityRepo: internetConnectivityRepo,
      ),

      BiExpiredTokenInterceptor(
        logger: logger,
        tokenManager: tokenManager,
        onExpired: () async {
          if (deviceSetupRepository.lastKnownDeviceType ==
              ResolvedDeviceType.mdm) {
            await embarkAuthenticationRepository.tokenManager.getToken();
          } else {
            final currentUserSession =
                embarkAuthenticationRepository.getCurrentUserSession();
            final isUserEmpty = currentUserSession == User.empty;
            if (!isUserEmpty) {
              final l10n = await appRepository.loadL10n();
              appRepository.bannerMessageSink.add(
                BannerMessage(text: l10n.sessionExpiredMessage),
              );
            }
            await embarkAuthenticationRepository.logOut();
          }
        },
        cancelRequestOnExpiredToken: (requestOptions) {
          final requestUri = requestOptions.uri.toString();
          final isConnectRequest = requestUri.contains('api/connect');
          final isMdmDevice = deviceSetupRepository.lastKnownDeviceType ==
              ResolvedDeviceType.mdm;

          // Exclude connect requests and allow cancellation based on device type
          return !isConnectRequest && !isMdmDevice;
        },
      ),

      // Automatically retry 3 times, spaced apart by different intervals (1s, 3s, 5s)
      BiRequestRetryInterceptor(dio: dio, logger: logger),

      // Add the custom accept header: application/x.biapi.v<version>+json
      BiAcceptHeaderInterceptor(logger: logger, appVersion: appVersion),

      // add the accept language header
      BiAcceptLanguageHeaderInterceptor(
        logger: logger,
        lookupLanguage: () {
          final currentLanguageCode =
              appRepository.getStoredLocale().languageCode;

          // DE15576: BISTS doesn't like Turkish. It results in an invalidly
          // scoped access token that prevents web calls from succeeding and
          // returns a 403. If Turkish is selected, sending en-US as
          // Accept-Language
          if (currentLanguageCode == AppRepository.turkishLanguageCode) {
            return AppRepository.defaultLanguageCode;
          }

          return currentLanguageCode;
        },
      ),

      // automatically call to get domains when the first request for an AppId is made.
      // These domains are cached, and are handled internally.
      BiDomainUrlResolverInterceptor(
        logger: logger,
        fetchDomains: (appId) {
          // this must execute as the fetchDomains callback
          // so that it correctly checks the current status of the review user.
          return !isReviewUser(sharedPreferences)
              ? primaryCdn.getAppDomains(appId)
              : secondaryCdn.getAppDomains(appId);
        },
      ),

      // Apply a bearer token, if the tokenManager.getToken returns one.
      // If it doesn't return one, it won't apply the bearer token.
      BiBearerHeaderInterceptor(
        logger: logger,
        lookupToken: tokenManager.getToken,
      ),

      BiRequestFailedInterceptor(
        logger: logger,
        onFailed: (error, handler) async {
          final l10n = await appRepository.loadL10n();
          String? message;

          await logger.info(
            message: 'BiRequestFailedInterceptor: ${error.type}',
          );
          switch (error.type) {
            case DioExceptionType.badResponse:
              // let the caller handle bad responses, since that is likely a proper
              // error to be handled. These would be like validation errors.
              if (error.response?.statusCode == 403) {
                // US23805, DE13776, DE13777: Catch for phone not assigned to reset device setup
                deviceSetupRepository.reset();
              }
              break;
            case DioExceptionType.badCertificate:
              message = l10n.serverError;
              break;
            case DioExceptionType.cancel:
              // We only cancel requests for two reasons:
              // 1. No internet connection. The user sees a banner in the middle of the screen.
              // 2. Expired token. The user is kicked back to the login page.
              // ..so we don't need to add an extra error banner here.
              break;
            case DioExceptionType.sendTimeout:
            case DioExceptionType.connectionTimeout:
            case DioExceptionType.receiveTimeout:
            case DioExceptionType.connectionError:
              message = l10n.noInternetConnection;
              break;
            case DioExceptionType.unknown:
              message = l10n.genericError;
              break;
          }

          if (message != null) {
            appRepository.bannerMessageSink.add(
              ErrorBannerMessage(text: message),
            );
          }
        },
      ),

      BiSerialNumberInterceptor(
        logger: logger,
        lookupValue: () async {
          final sharedPrefs = getIt.get<SmartBandPreferences>();
          final serialNumber = sharedPrefs.getSbSerial();
          return serialNumber;
        },
      ),
    ]);
  }

  /// Applies or removes [Interceptor]'s for use with background isolate requests
  /// Specifically, this removes all UI specific interceptors and replaces the
  /// [BiBearerHeaderInterceptor] with a [BackgroundJobBearerHeaderInterceptor]
  /// instead.
  static void applyForBackgroundJob({
    required Dio dio,
    required BiLogger logger,
    required BiTokenManager tokenManager,
    required BackgroundJobCredentialService backgroundJobService,
    required DeviceSetupRepositoryDeviceType deviceSetupRepository,
  }) {
    final uiInterceptorTypes = [
      BiRequestFailedInterceptor,
      BiBearerHeaderInterceptor,
      BiExpiredTokenInterceptor,
      BiInternetRequiredInterceptor,
    ];
    // remove UI interceptors that don't apply to background jobs.
    dio.interceptors
        .removeWhere((i) => uiInterceptorTypes.contains(i.runtimeType));

    // add the interceptor to use for authenticating background requests
    dio.interceptors.add(
      BackgroundJobBearerHeaderInterceptor(
        logger: logger,
        backgroundJobCredentialService: backgroundJobService,
        tokenManager: tokenManager,
        deviceSetupRepository: deviceSetupRepository,
      ),
    );
  }

  static void applyForCdn({
    required Dio dio,
    required BiLogger logger,
    required AppSettings settings,
    required InternetConnectivityRepository internetConnectivityRepo,
  }) {
    dio.options.contentType = ContentType.json.value;
    dio.interceptors.addAll([
      BiRequestLoggingInterceptor(logger: logger),

      // terminate requests if no internet.
      _buildInternetRequiredInterceptor(
        logger: logger,
        internetConnectivityRepo: internetConnectivityRepo,
      ),
      BiRequestRetryInterceptor(dio: dio, logger: logger),
    ]);
  }

  static BiInternetRequiredInterceptor _buildInternetRequiredInterceptor({
    required BiLogger logger,
    required InternetConnectivityRepository internetConnectivityRepo,
  }) {
    return BiInternetRequiredInterceptor(
      logger: logger,
      hasInternet: () async =>
          (await internetConnectivityRepo.reachability).lastOrNull !=
          ConnectivityResult.none,
    );
  }
}

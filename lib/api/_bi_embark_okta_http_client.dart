import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/services/background_jobs/auth/auth.dart';

class BiEmbarkOktaHttpClient extends BiOktaHttpClient {
  final BackgroundJobCredentialService backgroundJobCredentialService;

  BiEmbarkOktaHttpClient({
    required super.dio,
    required super.logger,
    required super.oktaUsername,
    required super.oktaPassword,
    required this.backgroundJobCredentialService,
  });

  @override
  Future<OktaLoginResponse> login(LoginCredentials? loginCredentials) async {
    final response = await super.login(loginCredentials);

    if (loginCredentials != null && response.accessToken.isNotEmpty) {
      // login succeeded, so update the background job credentials to use
      await backgroundJobCredentialService.setCredentials(loginCredentials);
    }
    return response;
  }
}

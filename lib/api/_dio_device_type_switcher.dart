import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/api/_bi_embark_okta_http_client.dart';
import 'package:embark/api/_bi_embark_sts_http_client.dart';
import 'package:embark/app_settings.dart';
import 'package:embark/services/background_jobs/auth/_background_job_credential_service.dart';
import 'package:embark/services/_shared_preferences_service.dart';

class DioDeviceTypeSwitcher {
  final AppSettings _appSettings;
  final BiTokenManager _tokenManager;
  final Dio _dio;
  final BiLogger _logger;
  final SharedPreferencesService _sharedPreferences;
  final BackgroundJobCredentialService _backgroundJobCredentialService;

  const DioDeviceTypeSwitcher({
    required AppSettings appSettings,
    required BiTokenManager tokenManager,
    required Dio dio,
    required BiLogger logger,
    required SharedPreferencesService sharedPreferences,
    required BackgroundJobCredentialService backgroundJobCredentialService,
  })  : _appSettings = appSettings,
        _tokenManager = tokenManager,
        _dio = dio,
        _logger = logger,
        _sharedPreferences = sharedPreferences,
        _backgroundJobCredentialService = backgroundJobCredentialService;

  void applyDeviceAuthentication({required String phoneNumber}) {
    final interceptor = BiDeviceAuthenticationInterceptor(
      logger: _logger,
      lookupValue: () async => phoneNumber,
    );

    _tokenManager.setAuthenticator(
      BiEmbarkOktaHttpClient(
        dio: _dio,
        logger: _logger,
        oktaUsername: _appSettings.oktaUsername,
        oktaPassword: _appSettings.oktaPassword,
        backgroundJobCredentialService: _backgroundJobCredentialService,
      ),
    );

    _dio.options.biAppId = AppId.biMobile;
    _replaceInterceptor(interceptor);
  }

  void removeDeviceAuthentication() {
    _dio.options.biAppId = AppId.smartlink;
    _dio.interceptors.removeWhere(
      (i) => i is BiDeviceAuthenticationInterceptor,
    );

    _tokenManager.setAuthenticator(
      BiEmbarkStsHttpClient(
        dio: _dio,
        logger: _logger,
        clientId: _appSettings.stsClientId,
        clientSecret: _appSettings.stsClientSecret,
        reviewUserEmail: _appSettings.reviewUserUsername,
        sharedPreferences: _sharedPreferences,
        backgroundJobCredentialService: _backgroundJobCredentialService,
      ),
    );
  }

  void _replaceInterceptor(Interceptor interceptor) {
    final currentIdx = _dio.interceptors.indexWhere(
      (i) => i.runtimeType == interceptor.runtimeType,
    );

    if (currentIdx == -1) {
      // add to the end
      _dio.interceptors.add(interceptor);
      return;
    }

    // replace
    _dio.interceptors.replaceRange(currentIdx, currentIdx + 1, [interceptor]);
  }
}

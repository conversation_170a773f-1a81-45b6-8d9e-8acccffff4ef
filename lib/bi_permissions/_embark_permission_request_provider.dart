import 'package:bi_embark_l10n/l10n/extensions.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';

class BiEmbarkPermissionRequestProvider extends StatelessWidget {
  final Widget? child;
  final GlobalKey<NavigatorState>? navigatorKey;

  const BiEmbarkPermissionRequestProvider({
    super.key,
    this.navigatorKey,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BiPermissionRequestProvider(
      continueButtonText: context.l10n.continueCommon,
      cancelButtonText: context.l10n.cancel,
      navigatorKey: navigatorKey,
      create: (_) => getIt.get(),
      buildRationaleTitle: _permissionToRationaleTitle,
      buildRationale: _permissionToRationale,
      child: child,
    );
  }

  String _permissionToRationaleTitle(
    BuildContext context,
    BiPermission permission,
  ) {
    return context.l10n.permissionRequired;
  }

  String _permissionToRationale(BuildContext context, BiPermission permission) {
    return switch (permission.type) {
      BiPermissionType.notification =>
        context.l10n.notificationPermissionRationale,
      BiPermissionType.locationAlways =>
        context.l10n.locationAlwaysPermissionRationale,
      _ => context.l10n.genericPermissionRationale
    };
  }
}

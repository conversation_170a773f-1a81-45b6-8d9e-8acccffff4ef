import 'package:flutter/widgets.dart';

import 'package:embark/app_lifecycle/_app_lifecycle_event_listener_state.dart';

class AppLifecycleEventListener extends StatefulWidget
    with WidgetsBindingObserver {
  final Widget child;

  const AppLifecycleEventListener({
    super.key,
    required this.child,
  });

  @override
  // ignore: deprecated_member_use_from_same_package
  State<StatefulWidget> createState() => AppLifecycleEventListenerState();
}

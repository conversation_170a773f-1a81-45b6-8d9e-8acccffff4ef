import 'package:embark/app_lifecycle/_app_lifecycle_event_listener.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:embark/repositories/_device_setup_respository.dart';

/// This class is unneeded, Use the included [AppLifecycleListener]
///
@Deprecated('Use AppLifecycleListener')
class AppLifecycleEventListenerState extends State<AppLifecycleEventListener>
    with WidgetsBindingObserver {
  AppRepository appRepository =
      getIt.get<AppRepository>(instanceName: 'AppRepository');
  final deviceSetupRepository = getIt.get<DeviceSetupRepository>();
  bool _didHitPauseState = false;
  bool _didHitHiddenState = false;
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    //Initial state for stream
    appRepository.setAppLifecycleState(AppLifecycleState.resumed);

    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    // Pass the state for others
    getIt
        .get<AppRepository>(instanceName: 'AppRepository')
        .setAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        await getIt.get<AuthManager>().returnedFromBackground(
              didHitPauseState: _didHitPauseState,
              didHitHiddenState: _didHitHiddenState,
            );
        _didHitPauseState = false;
        _didHitHiddenState = false;
        break;
      case AppLifecycleState.inactive:
        // Nothing needs to be done here at the moment.
        break;
      case AppLifecycleState.paused:
        _didHitPauseState = true;
        await getIt.get<AuthManager>().wentToBackground();
        break;
      case AppLifecycleState.detached:
        // Nothing needs to be done here at the moment.
        break;
      case AppLifecycleState.hidden:
        _didHitHiddenState = true;
        break;
    }
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

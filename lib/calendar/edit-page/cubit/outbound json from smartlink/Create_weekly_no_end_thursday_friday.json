{"rules": {"attributes": [{"code": "Name", "codeId": 2, "display": "Appointment Name", "isRequired": true, "maxLength": 50, "type": "string", "value": "test"}, {"availableValues": [{"attributes": [{"availableValues": [{"attributes": [{"code": "StartDate", "codeId": 13, "display": "Appointment Start Date", "isRequired": true, "type": "DateTime", "value": "2024-02-07T14:18"}, {"code": "Duration", "codeId": 14, "display": "Appointment End Date", "isRequired": true, "maxValue": 10079, "minValue": 1, "type": "int", "value": "60"}], "display": "One Time", "type": "Recurrence", "value": "1"}, {"attributes": [{"code": "Interval", "codeId": 16, "display": "Appointment Recurs Every", "isRequired": true, "maxValue": 99, "minValue": 1, "type": "int", "value": "1"}, {"code": "StartDate", "codeId": 13, "display": "Recurrence Start Date", "isRequired": true, "type": "DateTime", "value": "2024-02-07T14:18"}, {"code": "Duration", "codeId": 14, "display": "Appointment End Date", "isRequired": true, "maxValue": 1439, "minValue": 1, "type": "int", "value": "60"}, {"code": "EndDate", "codeId": 15, "display": "Recurrence End Date", "type": "Date", "value": "2024-02-09"}], "display": "Daily", "type": "Recurrence", "value": "2"}, {"attributes": [{"code": "Interval", "codeId": 16, "display": "Appointment Recurs Every", "isRequired": true, "maxValue": 99, "minValue": 1, "type": "int", "value": "1"}, {"code": "StartDate", "codeId": 13, "display": "Recurrence Start Date", "isRequired": true, "type": "DateTime", "value": "2024-02-07T14:18"}, {"code": "Duration", "codeId": 14, "display": "Appointment End Date", "isRequired": true, "maxValue": 1440, "minValue": 1, "type": "int", "value": "60"}, {"availableValues": [{"display": "Mon", "value": "1"}, {"display": "<PERSON><PERSON>", "value": "2"}, {"display": "Wed", "value": "3"}, {"display": "<PERSON>hu", "value": "4"}, {"display": "<PERSON><PERSON>", "value": "5"}, {"display": "Sat", "value": "6"}, {"display": "Sun", "value": "0"}], "baseType": "int", "code": "Days", "codeId": 18, "display": "Days of Week", "isRequired": true, "type": "array", "value": "4,5"}, {"code": "EndDate", "codeId": 15, "display": "Recurrence End Date", "type": "Date"}], "display": "Weekly", "type": "Recurrence", "value": "3"}], "code": "Recurrence", "codeId": 5, "display": "Appointment Recurrence", "isRequired": true, "type": "int", "value": "3"}], "display": "Change Request", "type": "CalendarItemType", "value": "28"}], "code": "Type", "codeId": 3, "display": "Appointment Type", "isRequired": true, "type": "int", "value": "28"}, {"code": "CalItemComments", "codeId": 4, "display": "Appointment Comments", "maxLength": 512, "type": "string", "value": ""}], "code": "CalItem", "codeId": 1, "display": "CalendarItem", "type": "CalendarItem"}}
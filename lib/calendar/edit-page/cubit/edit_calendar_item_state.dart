import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:equatable/equatable.dart';

/// A field key is essentially a composite key made up of
/// the field codeId and, if part of a recurrence, the recurrence id.
///
/// This allows us to partition all the fields in a way that means all
/// value changes and requirements match only whatever the currently
/// selected recurence type (i.e. [parentRecurrenceId]) defines.
class Field<PERSON>ey extends Equatable {
  /// The recurrence type this field belongs to, if any.
  final CalendarAppointmentRecurrence? parentRecurrenceId;

  /// The codeId of the field. If no [parentRecurrenceId] is present
  /// the field is considered parentless.
  final CalendarRuleInfoCodeId fieldId;

  const FieldKey({
    required this.parentRecurrenceId,
    required this.fieldId,
  });

  factory FieldKey.parentlessField(CalendarRuleInfoCodeId fieldId) => FieldKey(
        parentRecurrenceId: null,
        fieldId: fieldId,
      );

  Field<PERSON>ey withFieldId(
    CalendarRuleInfoCodeId fieldId,
  ) =>
      FieldKey(
        parentRecurrenceId: parentRecurrenceId,
        fieldId: fieldId,
      );

  @override
  List<Object?> get props => [
        parentRecurrenceId,
        fieldId,
      ];

  @override
  String toString() {
    return parentRecurrenceId != null
        ? '$parentRecurrenceId:$fieldId'
        : fieldId.toString();
  }
}

class EditCalendarItemState extends Equatable {
  const EditCalendarItemState();

  @override
  List<Object?> get props => [];
}

class EditCalendarItemInitialState extends EditCalendarItemState {
  const EditCalendarItemInitialState() : super();
}

class EditCalendarItemLoadedState extends EditCalendarItemState {
  final int? calendarItemId;
  final CalendarRuleInfo item;
  final Map<FieldKey, CalendarRuleInfo> fields;
  final bool saving;

  bool get isChangeRequest => calendarItemId != null;

  @override
  List<Object?> get props => [
        calendarItemId,
        item,
        isChangeRequest,
        fields,
        saving,
      ];

  const EditCalendarItemLoadedState({
    this.calendarItemId,
    required this.item,
    required this.fields,
    required this.saving,
  });

  EditCalendarItemLoadedState copyWith({
    bool? saving,
  }) =>
      EditCalendarItemLoadedState(
        item: item,
        fields: fields,
        saving: saving ?? this.saving,
      );
}

class EditCalendarItemApiCallSuccessState extends EditCalendarItemState {
  final ApiResponse apiResponse;

  const EditCalendarItemApiCallSuccessState({
    required this.apiResponse,
  });

  @override
  List<Object?> get props => [apiResponse];
}

class EditCalendarItemSubmittedState extends EditCalendarItemState {
  const EditCalendarItemSubmittedState();

  @override
  List<Object?> get props => [];
}

class EditCalendarItemCloseDeleteDialogState extends EditCalendarItemState {
  final int recurrenceId;
  final String date;
  final int? calendarItemId;
  final bool saving;

  const EditCalendarItemCloseDeleteDialogState({
    required this.recurrenceId,
    required this.date,
    required this.calendarItemId,
    required this.saving,
  });

  bool get isChangeRequest => calendarItemId != null;

  @override
  List<Object?> get props => [
        recurrenceId,
        date,
        calendarItemId,
        saving,
      ];

  EditCalendarItemCloseDeleteDialogState copyWith({
    bool? saving,
  }) =>
      EditCalendarItemCloseDeleteDialogState(
        recurrenceId: recurrenceId,
        date: date,
        calendarItemId: calendarItemId,
        saving: saving ?? this.saving,
      );
}

// ignore_for_file: deprecated_member_use_from_same_package

import 'dart:async';

import 'package:bi_flutter_login_widget/authentication/authentication_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/authentication/embar_authenticated_status_listener.dart';
import 'package:embark/navigation_observers/_arrived_on_dashboard_observer.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_status.dart';
import 'package:embark/pin/reset/check_in_result.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/repositories/_embark_authentication_repository.dart';
import 'package:embark/routing/_embark_routes.dart';
import 'package:embark/routing/navigator_extensions.dart';
import 'package:embark/services/_permission_handler.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';

/// This is meant to be the entrypoint into the application
/// after the device is considered to be "setup"
class EmbarkAuthenticationProvider extends StatefulWidget {
  final GlobalKey<NavigatorState> navigatorKey;
  final Widget child;
  final AuthenticationRoutes routes;
  const EmbarkAuthenticationProvider({
    super.key,
    required this.navigatorKey,
    required this.child,
    this.routes = const AuthenticationRoutes(),
  });

  @override
  State<EmbarkAuthenticationProvider> createState() =>
      _EmbarkAuthenticationProviderState();
}

class _EmbarkAuthenticationProviderState
    extends State<EmbarkAuthenticationProvider> with RouteAware {
  final authBloc = getIt.get<EmbarkAuthenticationBloc>();
  final tokenManager = getIt.get<BiTokenManager>();
  final EmbarkAuthenticationRepository embarkAuthenticationRepository =
      getIt.get<EmbarkAuthenticationRepository>();
  final PermissionHandler permissionHandler = getIt.get<PermissionHandler>();
  @override
  void initState() {
    authBloc.add(const ProviderOnScreenEvent(providerShown: true));
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: authBloc,
      child: _AuthenticationChangeListener(
        permissionHandler: permissionHandler,
        navigatorKey: widget.navigatorKey,
        routes: widget.routes,
        child: widget.child,
      ),
    );
  }

  @override
  void dispose() {
    authBloc.add(const ProviderOnScreenEvent(providerShown: false));
    super.dispose();
  }
}

class AuthenticationRoutes {
  const AuthenticationRoutes();
  Route<CheckInResult> get checkIn => EmbarkRoutes.checkIn();
}

class _AuthenticationChangeListener extends StatelessWidget {
  final GlobalKey<NavigatorState> navigatorKey;
  final Widget child;
  final AuthenticationRoutes routes;
  final PermissionHandler permissionHandler;

  const _AuthenticationChangeListener({
    required this.permissionHandler,
    required this.navigatorKey,
    required this.child,
    required this.routes,
  });

  @override
  Widget build(BuildContext providerOnlyContext) {
    return MultiBlocListener(
      listeners: [
        BlocListener<EmbarkAuthenticationBloc, AuthenticationState>(
          listenWhen: (previous, current) {
            final value =
                previous.status == EmbarkAuthenticationStatus.enrollment &&
                    current.status.isAuthenticated;
            return value;
          },
          listener: (_, state) {
            void navigate() {
              final navigationContext = navigatorKey.currentContext!;
              Future(
                navigationContext.mounted
                    ? () {
                        Navigator.of(navigationContext).pushAndRemoveUntil(
                          routes.checkIn,
                          (route) => route.isFirst,
                        );
                      }
                    : () {},
              );
              getIt.get<ArrivedOnDashboardObserver>().removeListener(navigate);
            }

            getIt.get<ArrivedOnDashboardObserver>().addListener(navigate);
          },
        ),
        BlocListener<EmbarkAuthenticationBloc, AuthenticationState>(
          listener: (_, state) async {
            // navigator.of isn''t available here, so use the _navigatorKey, which
            // is gross, but I'm not looking to overhaul our navigation setup.
            final navigationContext = navigatorKey.currentContext!;

            switch (state.status.value) {
              case EmbarkAuthenticationStatus.languageSelectionStatusValue:
                navigationContext.showLanguageSelection();
                break;
              case EmbarkAuthenticationStatus.permissionsStatusValue:
                navigationContext.showPermissions();
                break;
              case EmbarkAuthenticationStatus.validatePinValue:
                navigationContext.showPinValidation((_) {
                  getIt.get<AuthManager>().runAuthSteps();
                  final pushNotificationListenerCubit =
                      BlocProvider.of<PushNotificationListenerCubit>(
                    providerOnlyContext,
                  );

                  pushNotificationListenerCubit.checkVoipCallFromBackground();
                  pushNotificationListenerCubit
                      .checkIfThereIsSavedAppOpenedNotification();
                });
                break;
              case AuthenticationStatusBase.unauthenticatedStatusValue:
                navigationContext.showLogin();
                break;
              case EmbarkAuthenticationStatus.setPinValue:
                navigationContext.showSetPin();
                break;
              case EmbarkAuthenticationStatus.resetPinValue:
                navigationContext.showPinReset();
                break;
              case EmbarkAuthenticationStatus.termsAndConditionValue:
                await navigationContext.showTermsAndConditions(
                  showAcceptButton: true,
                );
                break;
              case EmbarkAuthenticationStatus.enrollmentValue:
                await Navigator.of(navigationContext)
                    .push(EmbarkRoutes.facialEnrollmentPage);
                break;
              case EmbarkAuthenticationStatus.locationValue:
                await permissionHandler.request(Permission.location);
                await getIt.get<AuthManager>().runAuthSteps();
                break;
              default:
                break;
            }
          },
        ),
      ],
      child: EmbarkAuthenticatedStatusListener(
        navigatorKey: navigatorKey,
        dashboardStep: getIt.get(),
        child: child,
      ),
    );
  }
}

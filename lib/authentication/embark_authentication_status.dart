import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';

class EmbarkAuthenticationStatus extends AuthenticationStatusBase {
  static const String languageSelectionStatusValue = 'languageSelection';
  static const String permissionsStatusValue = 'permissions';
  // static const String reevaluatingStatusValue = 'reevaluating';
  static const String setPinValue = 'setPin';
  static const String resetPinValue = 'resetPin';
  static const String termsAndConditionValue = 'termsAndCondition';
  static const String validatePinValue = 'validatePin';
  static const String enrollmentValue = 'enrollment';
  static const String locationValue = 'location';

  const EmbarkAuthenticationStatus(
    super.value,
  );

  static const AuthenticationStatusBase languageSelection =
      EmbarkAuthenticationStatus(languageSelectionStatusValue);

  static const AuthenticationStatusBase permissions =
      EmbarkAuthenticationStatus(permissionsStatusValue);

  static const AuthenticationStatusBase setPin =
      EmbarkAuthenticationStatus(setPinValue);

  static const AuthenticationStatusBase resetPin =
      EmbarkAuthenticationStatus(resetPinValue);

  static const AuthenticationStatusBase termsAndCondition =
      EmbarkAuthenticationStatus(termsAndConditionValue);

  static const AuthenticationStatusBase validatePin =
      EmbarkAuthenticationStatus(validatePinValue);
  static const AuthenticationStatusBase enrollment =
      EmbarkAuthenticationStatus(enrollmentValue);
  static const AuthenticationStatusBase location =
      EmbarkAuthenticationStatus(locationValue);

  static final allEmbarkAuthStatus = {
    languageSelection,
    permissions,
    setPin,
    resetPin,
    termsAndCondition,
    validatePin,
    enrollment,
    location,
  };
}

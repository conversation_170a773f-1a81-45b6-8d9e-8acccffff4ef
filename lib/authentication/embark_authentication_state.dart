import 'package:bi_flutter_login_widget/authentication/authentication.dart';
import 'package:embark/authentication/embark_authentication_status.dart';

class EmbarkAuthenticationState extends AuthenticationState {
  const EmbarkAuthenticationState._({
    super.status,
  });

  const EmbarkAuthenticationState.languageSelection()
      : this._(status: EmbarkAuthenticationStatus.languageSelection);

  const EmbarkAuthenticationState.permissions()
      : this._(status: EmbarkAuthenticationStatus.permissions);

  const EmbarkAuthenticationState.setPin()
      : this._(status: EmbarkAuthenticationStatus.setPin);
  const EmbarkAuthenticationState.resetPin()
      : this._(status: EmbarkAuthenticationStatus.resetPin);

  const EmbarkAuthenticationState.validatePin()
      : this._(status: EmbarkAuthenticationStatus.validatePin);

  const EmbarkAuthenticationState.terms()
      : this._(status: EmbarkAuthenticationStatus.termsAndCondition);

  const EmbarkAuthenticationState.enrollment()
      : this._(status: EmbarkAuthenticationStatus.enrollment);

  const EmbarkAuthenticationState.location()
      : this._(status: EmbarkAuthenticationStatus.location);

  @override
  List<Object?> get props => [status];
}

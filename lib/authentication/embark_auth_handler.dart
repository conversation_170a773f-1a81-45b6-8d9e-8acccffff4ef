part of 'embark_authentication_bloc.dart';

class AuthHandler {
  AuthHandler({
    List<AuthStep> defaultAuthSteps = const [],
    List<AuthStep> runBeforeOtherAuthSteps = const [],
  })  : _defaultSteps = defaultAuthSteps,
        _runBeforeOtherAuthSteps = runBeforeOtherAuthSteps;
  AuthHandler.prod({
    required PostAuthenticationAuthStep postAuthenticationAuthStep,
    required GetAuthSettingsStep getSettings,
    required DefaultAuthStep defaultAuthStep,
    required MdmAuthHandler mdmAuthHandler,
  })  : _defaultSteps = [postAuthenticationAuthStep],
        _runBeforeOtherAuthSteps = [
          getSettings,
          mdmAuthHandler,
          defaultAuthStep,
        ];

  final List<AuthStep> _defaultSteps;
  final List<AuthStep> _runBeforeOtherAuthSteps;
  final List<AuthStep> _customAuthStep = [];
  @visibleForTesting
  List<AuthStep> get authSteps => [
        ..._defaultSteps,
        ..._customAuthStep,
        ..._runBeforeOtherAuthSteps,
      ];

  void attachAuthStep(AuthStep authStep) {
    assert(!_customAuthStep.contains(authStep));
    _customAuthStep.add(authStep);
    authStep.addedToStack();
  }

  void removeAuthStep(AuthStep authStep) {
    assert(_customAuthStep.contains(authStep));
    _customAuthStep.remove(authStep);
    authStep.removedFromStack();
  }

  @Deprecated('remove use internal')
  void appForegrounded({required bool didHitHiddenState}) {
    for (final item in authSteps) {
      item.appForegrounded(didHitHiddenState: didHitHiddenState);
    }
  }

  FutureOr<void> dispose() async {
    for (final item in authSteps) {
      await item.dispose();
    }
  }
}

mixin AuthStep {
  Future<AuthenticationState?> nextStep(
    AuthenticationStatusBase status,
    GetClientSettingsResponse? clientSettingsResponse,
  );
  @Deprecated('use AppLifecycleListener')
  void appForegrounded({required bool didHitHiddenState}) {}

  FutureOr<void> dispose() {}

  void addedToStack() {}
  void removedFromStack() {}

  bool isLogin = false;
}

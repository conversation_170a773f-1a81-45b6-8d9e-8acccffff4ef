import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';

class AuthStepProvider extends StatefulWidget {
  const AuthStepProvider({
    super.key,
    required this.authStep,
    required this.child,
  });
  final AuthStep authStep;
  final Widget child;

  @override
  State<AuthStepProvider> createState() => _AuthStepProviderState();
}

class _AuthStepProviderState extends State<AuthStepProvider> {
  late final _authManager = getIt.get<AuthManager>();
  late final _authHandler = getIt.get<AuthHandler>();
  @override
  void initState() {
    _authManager.attachAuthStep(widget.authStep);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AuthStepProvider oldWidget) {
    if (widget.authStep != oldWidget.authStep) {
      _authHandler.removeAuthStep(oldWidget.authStep);
      _authHandler.attachAuthStep(widget.authStep);
      _authManager.runAuthSteps();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _authManager.removeAuthStep(widget.authStep);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

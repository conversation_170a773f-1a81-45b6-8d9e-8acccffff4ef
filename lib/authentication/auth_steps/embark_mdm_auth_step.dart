import 'dart:async';

import 'package:bi_flutter_login_widget/authentication/authentication_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:flutter/cupertino.dart';

class MdmShowAuth {
  bool needsToShow = true;
  bool resetPin = false;
}

class MdmAuthHandler extends MdmShowAuth with AuthStep {
  MdmAuthHandler(
    this._deviceSetupRepository,
    this._clientSettingsRepository, {
    WidgetsBinding? binding,
  }) {
    _lifecycleListener = AppLifecycleListener(
      binding: binding,
      onHide: () {
        needsToShow = true;
        resetPin = false;
      },
    );
  }
  late final AppLifecycleListener _lifecycleListener;
  final DeviceSetupRepository _deviceSetupRepository;
  final ClientSettingsRepository _clientSettingsRepository;

  @override
  FutureOr<void> dispose() {
    _lifecycleListener.dispose();
  }

  @override
  Future<AuthenticationState?> nextStep(
    AuthenticationStatusBase status,
    GetClientSettingsResponse? clientSettingsResponse,
  ) async {
    if (_deviceSetupRepository.lastKnownDeviceType == ResolvedDeviceType.mdm) {
      final clientSettings = clientSettingsResponse ??
          await _clientSettingsRepository.reloadClientSettings();

      if (clientSettings != null &&
          (clientSettings.acceptedTerms == false ||
              clientSettings.acceptedTerms == null)) {
        return const EmbarkAuthenticationState.terms();
      }
      if (resetPin) {
        return const EmbarkAuthenticationState.resetPin();
      }
      if (clientSettings != null && clientSettings.pinSet != true) {
        // If client is registered for biometrics, then take them to reset pin workflow
        if (clientSettings.enrolledFacial == true) {
          return const EmbarkAuthenticationState.resetPin();
        }
        // Otherwise, take them to set pin
        return const EmbarkAuthenticationState.setPin();
      }

      if (needsToShow && clientSettings?.pinSet == true) {
        return const EmbarkAuthenticationState.validatePin();
      }
    }
    return null;
  }
}

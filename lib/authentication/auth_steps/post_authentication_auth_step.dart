import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/push_notifications/cubit/push_notifications_cubit.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_permission_handler.dart';
import 'package:permission_handler/permission_handler.dart';

// Do not add to this class
// Create new auth steps
class PostAuthenticationAuthStep with AuthStep {
  final DeviceInfoRepository _deviceInfoRepository;
  final EmbarkAuthenticationRepository _authenticationRepository;
  final DeviceSetupRepository _deviceSetupRepository;
  final PushNotificationsCubit _pushNotificationsCubit;
  final AppSettingsRepository _appSettingsRepository;
  final ClientSettingsRepository _clientSettingsRepository;
  // ignore: deprecated_member_use_from_same_package
  final PermissionHandler _permissionHandler;

  PostAuthenticationAuthStep({
    required DeviceInfoRepository deviceInfoRepository,
    required ClientSettingsRepository clientSettingsRepository,
    required EmbarkAuthenticationRepository authenticationRepository,
    required DeviceSetupRepository deviceSetupRepository,
    required PushNotificationsCubit pushNotificationsCubit,
    required AppSettingsRepository appSettingsRepository,
    // ignore: deprecated_member_use_from_same_package
    required PermissionHandler permissionHandler,
  })  : _deviceInfoRepository = deviceInfoRepository,
        _clientSettingsRepository = clientSettingsRepository,
        _authenticationRepository = authenticationRepository,
        _pushNotificationsCubit = pushNotificationsCubit,
        _deviceSetupRepository = deviceSetupRepository,
        _appSettingsRepository = appSettingsRepository,
        _permissionHandler = permissionHandler;

  bool _didHitHiddenState = true;

  @override
  void appForegrounded({required bool didHitHiddenState}) {
    _didHitHiddenState = didHitHiddenState;
  }

  @override
  Future<AuthenticationState?> nextStep(
    AuthenticationStatusBase status,
    GetClientSettingsResponse? clientSettingsResponse,
  ) async {
    if (status == AuthenticationStatusBase.unauthenticated) {
      // unauthenticated, so ensure client settings is reset so they get refreshed
      // upon login.
      _postedDeviceInfo = false;
    }
    AuthenticationState? next;
    if (status.isAuthenticated) {
      // if language change is pending upload, submit it to the server
      if (_appSettingsRepository.isLanguageChangePendingUpload) {
        await _appSettingsRepository.submitLanguageSelection();
        await _clientSettingsRepository.reloadClientSettings();
      }

      next = await _handlePostAuthentication(
        enrolledFacial: clientSettingsResponse?.enrolledFacial,
        acceptedTerms: clientSettingsResponse?.acceptedTerms,
      );
    }
    return next;
  }

  Future<AuthenticationState?> _handlePostAuthentication({
    required bool? acceptedTerms,
    required bool? enrolledFacial,
  }) async {
    final session = _authenticationRepository.getCurrentUserSession();
    if (session!.token.isEmpty) {
      return null;
    }
    final device = _deviceSetupRepository.lastKnownDeviceType;
    if (device == ResolvedDeviceType.mdm) {
      await _initializeForegroundNotifications();
    }
    if (isLogin) {
      isLogin = false;
      await _postDeviceInfo();
      await _initializeForegroundNotifications();
    }
    if (_deviceSetupRepository.lastKnownDeviceType == ResolvedDeviceType.byod) {
      if (acceptedTerms == null || acceptedTerms == false) {
        return const EmbarkAuthenticationState.terms();
      }
    }
    if (enrolledFacial == false) {
      return const EmbarkAuthenticationState.enrollment();
    }

    /// We ask for location permission only if
    /// 1. we came from background (from hidden state) so as to avoid loop
    /// that occurs when we ask for location permission and user denies it
    /// (OS popup dialog which puts the app in pause mode)
    /// 2. location permission is denied
    if (_didHitHiddenState &&
        await _permissionHandler.grantStatus(Permission.location) ==
            PermissionStatus.denied) {
      return const EmbarkAuthenticationState.location();
    }

    return null;
  }

  bool _postedDeviceInfo = false;
  Future<void> _postDeviceInfo() async {
    if (!_postedDeviceInfo) {
      _postedDeviceInfo = true;
      await _deviceInfoRepository.postDeviceInfo();
    }
  }

  Future<void> _initializeForegroundNotifications() async {
    await _pushNotificationsCubit.initializeForegroundNotifications();
  }
}

import 'package:bi_flutter_login_widget/authentication/authentication_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/repositories/_client_settings_repository.dart';
import 'package:flutter/widgets.dart';

class GetAuthSettingsStep with AuthStep {
  GetAuthSettingsStep({
    required ClientSettingsRepository clientSettingsRepository,
  }) : _clientSettingsRepository = clientSettingsRepository {
    _lifecycle = AppLifecycleListener(
      onRestart: () {
        _cameFromBackground = true;
      },
    );
  }
  @override
  void dispose() {
    _lifecycle.dispose();
  }

  late final AppLifecycleListener _lifecycle;
  final ClientSettingsRepository _clientSettingsRepository;
  bool _cameFromBackground = true;

  @override
  Future<AuthenticationState?> nextStep(
    AuthenticationStatusBase status,
    GetClientSettingsResponse? clientSettingsResponse,
  ) async {
    try {
      if (!status.isAuthenticated) {
        return null;
      }
      if (clientSettingsResponse == null || _cameFromBackground) {
        await _clientSettingsRepository.reloadClientSettings();
      }

      return null;
    } finally {
      _cameFromBackground = false;
    }
  }
}

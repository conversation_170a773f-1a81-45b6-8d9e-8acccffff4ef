import 'package:bi_flutter_login_widget/authentication/authentication_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/authentication/embark_authentication_state.dart';
import 'package:embark/authentication/embark_authentication_status.dart';
import 'package:embark/repositories/_client_settings_repository.dart';

class DefaultAuthStep with AuthStep {
  final ClientSettingsRepository _clientSettingsRepository;

  DefaultAuthStep({
    required ClientSettingsRepository clientSettingsRepository,
  }) : _clientSettingsRepository = clientSettingsRepository;

  GetClientSettingsResponse? get latestClientSettings =>
      _clientSettingsRepository.clientSettingsListener.value;

  @override
  Future<AuthenticationState?> nextStep(
    AuthenticationStatusBase status,
    GetClientSettingsResponse? clientSettingsResponse,
  ) async {
    if (status == EmbarkAuthenticationStatus.languageSelection) {
      return const EmbarkAuthenticationState.languageSelection();
    }

    if (status == EmbarkAuthenticationStatus.permissions) {
      return const EmbarkAuthenticationState.permissions();
    }

    if (status == AuthenticationStatusBase.unauthenticated) {
      // unauthenticated, so ensure client settings is reset so they get refreshed
      // upon login.
      await _clientSettingsRepository.clear();
    }

    return null;
  }
}

import 'dart:async';

import 'package:bi_flutter_login_widget/authentication/authentication_bloc.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';

class ShowingDashboardStep with AuthStep {
  bool _shouldShow = true;
  bool get shouldShow => _shouldShow;
  void goingToDashboard() {
    _shouldShow = false;
  }

  int _assertMultiple = 0;
  @override
  Future<AuthenticationState?> nextStep(
    AuthenticationStatusBase status,
    GetClientSettingsResponse? clientSettingsResponse,
  ) async {
    return null;
  }

  @override
  void removedFromStack() {
    _shouldShow = true;
    _assertMultiple--;
    assert(_assertMultiple == 0);
  }

  @override
  void addedToStack() {
    _shouldShow = false;
    _assertMultiple++;
    assert(_assertMultiple == 1);
  }
}

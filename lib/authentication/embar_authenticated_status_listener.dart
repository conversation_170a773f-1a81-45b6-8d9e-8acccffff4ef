import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/authentication/auth_steps/showing_dashboard_step.dart';
import 'package:embark/authentication/embark_authentication_bloc.dart';
import 'package:embark/push_notification_listener/cubit/push_notification_listener_cubit.dart';
import 'package:embark/push_notifications/models/models.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/routing/navigator_extensions.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EmbarkAuthenticatedStatusListener extends StatelessWidget {
  const EmbarkAuthenticatedStatusListener({
    super.key,
    required this.child,
    required this.navigator<PERSON><PERSON>,
    required this.dashboardStep,
  });
  final Widget child;
  final GlobalKey<NavigatorState> navigatorKey;
  final ShowingDashboardStep dashboardStep;

  BuildContext get navigationContext => navigatorKey.currentContext!;

  void _navigateToDashboard() {
    if (dashboardStep.shouldShow) {
      dashboardStep.goingToDashboard();
      navigationContext.showDashboard();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<EmbarkAuthenticationBloc, AuthenticationState>(
      listenWhen: (previous, current) {
        if (!current.status.isAuthenticated) {
          return false;
        }
        // should be moved to own listener
        if (current.status.returnedFromBackground) {
          return true;
        }
        if (!dashboardStep.shouldShow) {
          return false;
        }
        return true;
      },
      listener: (_, state) async {
        if (navigationContext.mounted == true) {
          _checkSavedBackgroundNotificationTap(navigationContext);
          // Be careful when using a BuildContext across an async gap.
        }

        getIt
            .get<AppRepository>(instanceName: 'AppRepository')
            .authenticationStatusSink
            .add(state.status);
        if (!state.status.returnedFromBackground && navigationContext.mounted) {
          final sharedPreferencesService =
              getIt.get<SharedPreferencesService>();

          final pendingJsonPushPayLoad = sharedPreferencesService.getJson(
            PushNotificationListenerCubit.pendingPushPayLoadPrefsKey,
          );

          if (pendingJsonPushPayLoad.isEmpty) {
            _navigateToDashboard();
          } else {
            final pushPayload = PushPayload.fromJson(
              pendingJsonPushPayLoad.map((key, value) {
                return MapEntry(key, value);
              }).cast<String, Object?>(),
            );

            _checkSavedPushPayLoad(navigationContext, pushPayload);
            await sharedPreferencesService.saveAsJson(
              PushNotificationListenerCubit.pendingPushPayLoadPrefsKey,
              {},
            );
          }
        }
      },
      child: child,
    );
  }

  void _checkSavedPushPayLoad(
    BuildContext context,
    PushPayload pushPayload,
  ) async {
    final PushNotificationListenerCubit pushNotificationListenerCubit =
        BlocProvider.of<PushNotificationListenerCubit>(context);
    switch (pushPayload.actionType) {
      case PushNotificationType.message:
        pushNotificationListenerCubit.startingDashBoardTabId =
            PluginType.conversation.value;
        pushNotificationListenerCubit
            .setStartingDashBoardTabId(PluginType.conversation.value);
        _navigateToDashboard();

      case PushNotificationType.calendar:
      case PushNotificationType.reminder:
        pushNotificationListenerCubit
            .setStartingDashBoardTabId(PluginType.calendar.value);
        _navigateToDashboard();

      case PushNotificationType.media:
        pushNotificationListenerCubit
            .setStartingDashBoardTabId(PluginType.myDocuments.value);
        _navigateToDashboard();

      case PushNotificationType.checkin:
        context.showCheckIn();

      case PushNotificationType.report:
        await context.showSelfReport();

      case PushNotificationType.video:
        _navigateToDashboard();

      default:
        _navigateToDashboard();
    }
  }

  void _checkSavedBackgroundNotificationTap(BuildContext context) {
    final PushNotificationListenerCubit pushNotificationListenerCubit =
        BlocProvider.of<PushNotificationListenerCubit>(context);

    if (pushNotificationListenerCubit.startingDashBoardTabId != -1) {
      _navigateToDashboard();
    }
  }
}

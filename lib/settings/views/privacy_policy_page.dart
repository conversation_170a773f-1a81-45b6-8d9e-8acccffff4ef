import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:embark/repositories/_device_setup_respository.dart';
import 'package:embark/resources/utils/url_launcher_util.dart';
import 'package:embark/routing/routing.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    final loader = getIt.get<L10nAssetLoader>();
    return FutureBuilder(
      future:
          loader.loadPrivacyPolicy(context: context, locale: l10n.localeName),
      builder: (context, html) {
        if (html.connectionState != ConnectionState.done) {
          return const SizedBox.shrink();
        }
        return BiLocalHtmlPage(
          title: l10n.privacyPolicyTitle,
          html: html.data!,
          onLinkTap: (phoneNumber, _, element) {
            final deviceSetupRepo = getIt.get<DeviceSetupRepository>();
            final isByod =
                deviceSetupRepo.lastKnownDeviceType == ResolvedDeviceType.byod
                    ? true
                    : false;
            if (phoneNumber!.startsWith('tel:')) {
              onPhoneNumberClick(
                context: context,
                isByod: isByod,
                phoneNumber: phoneNumber.substring(4),
              );
            }
          },
        );
      },
    );
  }
}

Future<void> onPhoneNumberClick({
  required BuildContext context,
  required bool isByod,
  required String phoneNumber,
}) async {
  final l10n = AppLocalizations.of(context);
  if (isByod) {
    final error =
        await UrlLauncherUtil.launchUrlUtil('tel://$phoneNumber', l10n);

    if (context.mounted && error == l10n.badLink) {
      errorLauncher(context, l10n.genericError);
    }
  } else {
    final SharedPreferencesService sharedPreferencesService =
        getIt.get<SharedPreferencesService>();
    await sharedPreferencesService.setString(
      SharedPreferencesKeys.incomingOutsideNumber.key,
      phoneNumber,
    );

    if (context.mounted) {
      context.startVoipCall(
        phoneNumber,
        onPhoneCallEnds: () => Navigator.pop(context),
      );
    }
  }
}

void errorLauncher(BuildContext context, String message) {
  context.showBannerMessage(ErrorBannerMessage(text: message));
}

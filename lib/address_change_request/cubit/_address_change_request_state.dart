import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:equatable/equatable.dart';

enum AddressFormStatus {
  loading,
  loaded,
}

class AddressChangeRequestState extends Equatable {
  final AddressFormStatus status;
  final AddressRules? rules;
  final AddressResponse? currentRequest;

  const AddressChangeRequestState(
    this.status, {
    this.rules,
    this.currentRequest,
  });

  factory AddressChangeRequestState.initial() =>
      const AddressChangeRequestState(AddressFormStatus.loading);

  factory AddressChangeRequestState.loaded(
    AddressRules rules,
    AddressResponse? currentRequest,
  ) =>
      AddressChangeRequestState(
        AddressFormStatus.loaded,
        rules: rules,
        currentRequest: currentRequest,
      );

  @override
  List<Object?> get props => [
        status,
        rules,
        currentRequest,
      ];

  AddressChangeRequestState copyWith({
    AddressFormStatus? status,
    AddressRules? rules,
    AddressResponse? currentRequest,
  }) =>
      AddressChangeRequestState(
        status ?? this.status,
        rules: rules ?? this.rules,
        currentRequest: currentRequest ?? this.currentRequest,
      );
}

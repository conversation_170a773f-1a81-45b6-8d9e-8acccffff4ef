import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:collection/collection.dart';
import 'package:embark/address_change_request/cubit/_address_change_request_state.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:helpers/helpers.dart';
import 'package:bi_embark_l10n/bi_embark_l10n.dart';

class AddressChangeRequestCubit
// ignore: deprecated_member_use, deprecated_member_use_from_same_package
    extends BiEmbarkCubit<AddressChangeRequestState> {
  final AddressRepository _addressRepository;
  late AppLocalizations _l10n;

  AddressChangeRequestCubit(
    this._addressRepository,
  ) : super(AddressChangeRequestState.initial());

  Future<void> init({
    required AppLocalizations l10n,
  }) async {
    try {
      _l10n = l10n;

      final rules = await _addressRepository.getRules();
      final existingChanges = await _addressRepository.load();
      final updatedTimeZoneChanges =
          updateTimeZoneChanges(rules, existingChanges);
      if (rules.states.isEmpty || rules.timeZones.isEmpty) {
        // the form attempts to pick the first state/timezone, so if there are no
        // values, which shouldn't ever happen, show the generic error screen.
        return setFatalError(_l10n.genericError);
      }

      emit(
        AddressChangeRequestState.loaded(rules, updatedTimeZoneChanges),
      );
    } catch (err, stack) {
      setFatalError(err, stack);
    }
  }

  AddressResponse updateTimeZoneChanges(
    AddressRules rules,
    AddressResponse existingChanges,
  ) {
    final initialTimeZoneSelection = rules.timeZones.firstWhereOrNull(
      (tz) => tz.id == existingChanges.timeZoneId,
    );
    existingChanges = existingChanges.copyWith(
      address2: existingChanges.address2 ?? '',
      address3: existingChanges.address3 ?? '',
      timeZone: initialTimeZoneSelection?.name,
      timeZoneId: initialTimeZoneSelection?.id,
    );
    return existingChanges;
  }
}

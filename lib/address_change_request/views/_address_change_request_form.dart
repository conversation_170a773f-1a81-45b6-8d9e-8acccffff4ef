import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/address_change_request/cubit/_address_change_request_cubit.dart';
import 'package:embark/address_change_request/cubit/_address_change_request_state.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:widgets/widgets.dart';

class AddressChangeRequestForm extends StatelessWidget {
  /// A callback used when the underlying address form has valid changes.
  final dynamic Function(AddressResponse?)? onReady;

  const AddressChangeRequestForm({
    super.key,
    this.onReady,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final connectionStatus = getIt.get<Stream<List<ConnectivityResult>>>();
    // ignore: deprecated_member_use
    return BiEmbarkBlocProvider<AddressChangeRequestCubit,
        AddressChangeRequestState>(
      create: (_) => getIt.get<AddressChangeRequestCubit>(),
      init: (cubit) => cubit.init(l10n: l10n),
      internetConnectivityStream: connectionStatus,
      child: BlocConsumer<AddressChangeRequestCubit, AddressChangeRequestState>(
        listener: (context, state) {
          if (state.status == AddressFormStatus.loaded) {
            onReady?.call(state.currentRequest);
          }
        },
        builder: (context, state) {
          if (state.status != AddressFormStatus.loaded) {
            return const BiFormLoading();
          }

          assert(state.rules != null);

          return BiEmbarkAddressFormFields(
            rules: state.rules!,
            initialValues: state.currentRequest,
          );
        },
      ),
    );
  }
}

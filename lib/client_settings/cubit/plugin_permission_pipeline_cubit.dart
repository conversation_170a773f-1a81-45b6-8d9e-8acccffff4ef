import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/bi_permissions.dart';

import 'package:embark/client_settings/cubit/bi_permission_extensions.dart';
import 'package:embark/client_settings/cubit/plugin_permission_pipeline_state.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PluginPermisssionPipelineCubit
    extends Cubit<PluginPermisssionPipelineState> {
  final ClientSettingsRepository _clientSettingsRepository;
  final BiPermissionRequestRepository _permissionRequestRepository;
  final EmbarkPermissionPreferences _preferences;

  PluginPermisssionPipelineCubit(
    this._clientSettingsRepository,
    this._permissionRequestRepository,
    this._preferences,
  ) : super(
          const PluginPermisssionPipelineState(
            status: PluginPermisssionPipelineStatus.initializing,
          ),
        ) {
    _clientSettingsRepository.clientSettingsListener.addListener(
      _clientSettingsChanged,
    );
  }

  @override
  Future<void> close() async {
    _clientSettingsRepository.clientSettingsListener
        .removeListener(_clientSettingsChanged);
    return super.close();
  }

  Future<void> initialize() => _clientSettingsChanged();

  Future<void> onResume() async {
    if (state.status == PluginPermisssionPipelineStatus.blocked) {
      // blocked, so refresh the status of things.
      final granted = await state.blockedBy!.granted;
      final supportsBlocking = state.blockedBy!.canBlockUiOnFirstDenial;

      if (!granted && supportsBlocking) {
        //  still blocking, so don't do anything.
        return;
      }

      if (granted) {
        // we changed to granted. Set to idle and progress to next one.
        emit(
          PluginPermisssionPipelineState(
            status: PluginPermisssionPipelineStatus.idle,
            shownChild: state.shownChild,
            evaluatedPermissions: state.evaluatedPermissions,
          ),
        );
        await _clientSettingsChanged();
      }
    }
  }

  Future<void> _clientSettingsChanged() async {
    final plugins =
        _clientSettingsRepository.clientSettingsListener.value?.pluginMap;

    if (plugins?.isNotEmpty != true) {
      // no plugins, so just show the child
      emit(
        state.copyWith(
          status: PluginPermisssionPipelineStatus.ready,
          shownChild: true,
        ),
      );
      return;
    }

    final permissionsToRequest = _getRequiredPermissionsForPlugins(plugins);

    if (state.evaluatedPermissions.containsAll(permissionsToRequest)) {
      // no change in required permissions, so just return.
      emit(
        state.copyWith(
          status: PluginPermisssionPipelineStatus.ready,
          shownChild: true,
        ),
      );
      return;
    }

    emit(
      state.copyWith(
        status: PluginPermisssionPipelineStatus.evaluating,
        evaluatedPermissions: permissionsToRequest.toSet(),
      ),
    );

    for (final permission in permissionsToRequest) {
      if (await permission.granted) {
        continue;
      }
      final supportsBlockingUi = permission.canBlockUiOnFirstDenial;
      final blockIfNotGranted = _preferences.showBlockingPage(permission.type);

      final granted =
          await _permissionRequestRepository.requestPermission(permission);

      await _preferences.hideBlockingPage(permission.type, true);

      final nextState = supportsBlockingUi && blockIfNotGranted && !granted
          ? PluginPermisssionPipelineStatus.blocked
          : PluginPermisssionPipelineStatus.idle;

      emit(
        state.copyWith(
          blockedBy: nextState == PluginPermisssionPipelineStatus.blocked
              ? permission
              : null,
          status: nextState,
        ),
      );
      // we were blocked by something, so stop it.
      if (state.status == PluginPermisssionPipelineStatus.blocked) break;
    }

    if (state.status != PluginPermisssionPipelineStatus.blocked) {
      emit(
        state.copyWith(
          status: PluginPermisssionPipelineStatus.ready,
          shownChild: true,
        ),
      );
    }
  }

  List<BiPermission> _getRequiredPermissionsForPlugins(
    Map<PluginType, PluginResponse>? plugins,
  ) {
    final permissionsToRequest = <BiPermission>[];

    for (final pluginType in plugins!.keys) {
      if (pluginType == PluginType.smartband) {
        permissionsToRequest.add(
          _permissionRequestRepository.supportedPermissions.bluetooth,
        );
      } else if (pluginType == PluginType.tracking) {
        permissionsToRequest.addAll([
          _permissionRequestRepository.supportedPermissions.locationWhenInUse,
          _permissionRequestRepository.supportedPermissions.locationAlways,
        ]);
      }
    }
    return permissionsToRequest;
  }

  static T select<T>(
    BuildContext context,
    T Function(PluginPermisssionPipelineState) selector,
  ) =>
      context.select<PluginPermisssionPipelineCubit, T>(
        (s) => selector(s.state),
      );

  static PluginPermisssionPipelineCubit read(
    BuildContext context,
  ) =>
      context.read<PluginPermisssionPipelineCubit>();
}

import 'package:bi_permissions/bi_permissions.dart';

extension BiPermissionExtensions on BiPermission {
  bool get canBlockUiOnFirstDenial {
    return switch (type) {
      // this shows blocking permission page if denied the very first time.
      BiPermissionType.bluetooth => true,
      // TODO: Will eventually need to change this to be true when we add
      // support for showing the blocking page when the user first denies location always.
      BiPermissionType.locationAlways => false,
      _ => false
    };
  }
}

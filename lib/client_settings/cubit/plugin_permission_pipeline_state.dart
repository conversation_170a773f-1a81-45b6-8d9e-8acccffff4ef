import 'package:bi_permissions/bi_permissions.dart';
import 'package:equatable/equatable.dart';

enum PluginPermisssionPipelineStatus {
  initializing,
  evaluating,
  blocked,
  idle,
  ready
}

class PluginPermisssionPipelineState extends Equatable {
  final BiPermission? blockedBy;
  final PluginPermisssionPipelineStatus status;
  final bool shownChild;
  final Set<BiPermission> evaluatedPermissions;

  const PluginPermisssionPipelineState({
    this.blockedBy,
    required this.status,
    this.shownChild = false,
    this.evaluatedPermissions = const {},
  });

  PluginPermisssionPipelineState copyWith({
    BiPermission? blockedBy,
    PluginPermisssionPipelineStatus? status,
    bool? shownChild,
    Set<BiPermission>? evaluatedPermissions,
  }) =>
      PluginPermisssionPipelineState(
        status: status ?? this.status,
        blockedBy: blockedBy ?? this.blockedBy,
        shownChild: shownChild ?? this.shownChild,
        evaluatedPermissions: evaluatedPermissions ?? this.evaluatedPermissions,
      );

  @override
  List<Object?> get props => [
        blockedBy,
        status,
        shownChild,
        evaluatedPermissions,
      ];
}

import 'package:bi_permissions/bi_permissions.dart';

import 'package:embark/client_settings/cubit/plugin_permission_pipeline_cubit.dart';
import 'package:embark/client_settings/cubit/plugin_permission_pipeline_state.dart';
import 'package:embark/starter.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PluginPermissionPipeline extends StatelessWidget {
  final Widget child;
  final Widget Function(BuildContext context, BiPermissionType blockedBy)?
      blockedPageBuilder;

  const PluginPermissionPipeline({
    super.key,
    this.blockedPageBuilder,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => PluginPermisssionPipelineCubit(
        getIt.get(),
        getIt.get(),
        getIt.get(),
      )..initialize(),
      child: _PluginPermissionLifecycleListener(
        child: _PluginPermissionPipelineBuilder(
          blockedPageBuilder: blockedPageBuilder,
          child: child,
        ),
      ),
    );
  }
}

class _PluginPermissionLifecycleListener extends StatefulWidget {
  final Widget child;

  const _PluginPermissionLifecycleListener({required this.child});
  @override
  State<StatefulWidget> createState() =>
      _PluginPermissionLifecycleListenerState();
}

class _PluginPermissionLifecycleListenerState
    extends State<_PluginPermissionLifecycleListener>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      PluginPermisssionPipelineCubit.read(context).onResume();
    }
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

class _PluginPermissionPipelineBuilder extends StatelessWidget {
  final Widget child;
  final Widget Function(BuildContext context, BiPermissionType blockedBy)?
      blockedPageBuilder;

  const _PluginPermissionPipelineBuilder({
    this.blockedPageBuilder,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final shownChild =
        PluginPermisssionPipelineCubit.select(context, (s) => s.shownChild);

    final status =
        PluginPermisssionPipelineCubit.select(context, (s) => s.status);

    if (status == PluginPermisssionPipelineStatus.blocked) {
      final blockedBy =
          PluginPermisssionPipelineCubit.select(context, (s) => s.blockedBy!);

      return Builder(
        builder: (context) =>
            blockedPageBuilder?.call(context, blockedBy.type) ??
            const SizedBox.shrink(),
      );
    } else if (status != PluginPermisssionPipelineStatus.ready && !shownChild) {
      return const SizedBox.shrink();
    } else {
      return child;
    }
  }
}

import 'dart:async';

import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_flutter_firebase_logging/bi_flutter_firebase_logging.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:flutter/material.dart';

class AppSettingsRepository {
  final AppRepository _appRepository;
  final SmartLinkAppSettingsHttpClient _appSettingsApi;
  final SharedPreferencesService _sharedPreferencesService;
  final BiLogger _logger;

  // these values come from the TA language code table
  final Map<String, int> _languageLookUpMap = {
    'en': 53,
    'es': 156,
    'fr': 75,
    'hi': 98,
    'ht': 301,
    'pa': 142,
    'pt': 139,
    'ro': 144,
    'ru': 146,
    'tr': 192,
    'uk': 194,
    'vi': 201,
    'zh': 40,
    'ar': 5,
    'bn': 214,
    'so': 212,
    'ur': 196,
  };
  final languagePendingUploadKey = 'languagePendingUpload';
  final List<StreamSubscription<dynamic>> _subs = [];

  AppSettingsRepository(
    this._appSettingsApi,
    this._sharedPreferencesService,
    this._appRepository,
    this._logger,
  ) {
    _subs.add(
      _appRepository.authenticationStatusStream.listen((status) async {
        if (status.isAuthenticated && isLanguageChangePendingUpload) {
          // if there is a language change prior to login that we need to upload, do
          // it now that we are authenticated.
          await submitLanguageSelection();
        }
      }),
    );
  }

  Future<void> close() async {
    for (final sub in _subs) {
      await sub.cancel();
    }
  }

  /// adds locale to stream in app.dart
  /// will change app localization
  void setAppLocale(Locale locale) {
    _appRepository.setLocale(locale);
    if (locale != const Locale('en')) {
      _appRepository.setTranslateMessages(true);
      _appRepository.storeTranslateMessages(true);
    }
  }

  /// Stores the [locale] locally and does not emit the new locale to the
  /// locale stream, nor does it post the language change to the API.
  ///
  /// If you want to fully trigger a language change in the application,
  /// use the [storeAndSetAppLocaleIfChanged] method instead.
  Future<void> storeAppLocale(Locale locale) async {
    // store selected locale
    await _sharedPreferencesService.setString(
      _appRepository.localeKey,
      locale.toLanguageTag(),
    );
  }

  Future<void> _setLanguageChangeNoLongerPendingUpload() =>
      _sharedPreferencesService.setBool(languagePendingUploadKey, false);

  Future<void> setLanguageChangePendingUpload() =>
      _sharedPreferencesService.setBool(languagePendingUploadKey, true);

  /// Looks up the associated [Locale] that matches the [apiLanguageCodeId] and
  /// persists it to local storage. It will also emit the [Locale] change to the
  /// locale stream so that any listeners can update their UI accordingly.
  ///
  /// In the event that the current locale and the [apiLanguageCodeId] locale are
  /// the same, nothing will change, and no event will be emitted to the stream.
  ///
  /// Note: This does not submit the [apiLanguageCodeId] to the API, this only
  /// handles app side changes that are required. If you need to submit the change
  /// to the API, call [submitLanguageSelection] after calling
  /// [storeAndSetAppLocaleIfChanged].
  Future<Locale?> storeAndSetAppLocaleIfChanged(int apiLanguageCodeId) async {
    final currentLocale = _appRepository.getStoredLocale();
    final targetLocale = getLocale(apiLanguageCodeId);

    // same locale, so nothing to really do here.
    if (currentLocale == targetLocale) return null;

    // language changed.
    // This will schedule a language change, which should update the UI everywhere.
    await _logger.info(
      message:
          'Changing app language from ${currentLocale.languageCode} to ${targetLocale.languageCode}',
      debugProps: {'targeLanguageCodeId': apiLanguageCodeId},
    );
    await storeAppLocale(targetLocale);
    setAppLocale(targetLocale);
    return targetLocale;
  }

  bool get isLanguageChangePendingUpload =>
      _sharedPreferencesService.getBool(languagePendingUploadKey, false);

  /// lets our servers know the clients preferred language
  Future<void> submitLanguageSelection() async {
    // if we have already successfully sent this, we will not do it again.
    // this value is reset in LanguageRepository.storeAppLocale when
    // we save a new locale
    if (!_appRepository.hasSelectedLanguage) {
      return;
    }

    //send locale to servers SmartlinkAppSettings
    try {
      final locale = _appRepository.getStoredLocale();

      await _appSettingsApi.postEventsLanguage(
        getLanguageId(locale.languageCode),
      );
      // save that we successfully sent preferred language
      await _setLanguageChangeNoLongerPendingUpload();
    } on ApiResponse {
      // save that sending failed so we can try again next time
      await setLanguageChangePendingUpload();
    }
  }

  /// Looks up a supported [Locale] based on the API's [languageCodeId] value.
  /// If no supported/matching [Locale] is found, then English is returned as the
  /// default.
  Locale getLocale(int languageCodeId) {
    Locale? locatedLocale;
    for (final kv in _languageLookUpMap.entries) {
      if (kv.value == languageCodeId) {
        locatedLocale = Locale(kv.key);
        break;
      }
    }

    if (locatedLocale == null ||
        !AppLocalizations.supportedLocales.contains(locatedLocale)) {
      // no mapped value (or it was mapped but not supported currently), so default to english.
      return const Locale('en');
    }

    return locatedLocale;
  }

  int getLanguageId(String languageCode) =>
      _languageLookUpMap.containsKey(languageCode)
          ? _languageLookUpMap[languageCode]!
          : 0;

  Future<void> postAcceptTermsAndConditions() async {
    await _appSettingsApi.postAcceptTerms();
  }
}

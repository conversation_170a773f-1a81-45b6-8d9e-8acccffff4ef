import 'dart:async';

import 'package:bi_flutter_bluetooth_platform_api/models/models.dart';
import 'package:bi_flutter_bluetooth_platform_api/plugin/plugin.dart';
import 'package:embark/repositories/_biometric_repository.dart';
import 'package:embark/repositories/_sensor_event_info_repository.dart';
import 'package:embark/repositories/smart_band/_smart_band_repository.dart';
import 'package:embark/services/services.dart';
import 'package:embark/smartband/utilities/extensions.dart';
import 'package:flutter/foundation.dart';

enum SharedPreferencesKeyUpdateIsolateMethod {
  serial('sb_serial');

  final String value;
  const SharedPreferencesKeyUpdateIsolateMethod(this.value);
}

/// Used to process SmartBand events when running as a background job. This
/// class is not meant to be used by the main UI isolate. If you need access
/// to UI specific operations, use the [SmartBandUiRepository] instead.
class SmartBandBackgroundRepository extends SmartBandRepository {
  final SmartBandPreferences _sharedPreferencesService;
  final SensorEventInfoRepository _sensorEventInfoRepository;
  final BiometricRepository _biometricRepository;

  CentralPlatform get _centralPlatform =>
      defaultTargetPlatform == TargetPlatform.iOS
          ? CentralPlatform.ios
          : CentralPlatform.android;

  const SmartBandBackgroundRepository({
    required super.sharedPreferencesService,
    required super.bluetoothApi,
    required SensorEventInfoRepository sensorEventInfoRepository,
    required BiometricRepository biometricRepository,
  })  : _sharedPreferencesService = sharedPreferencesService,
        _sensorEventInfoRepository = sensorEventInfoRepository,
        _biometricRepository = biometricRepository;

  /// Processes a single SmartBand [event] and uploads the event to TA if it is
  /// required. If the [event] is a [ConnectionChangedEvent], the [controller] will be used to
  /// update the cached FW version and Serial Number in the app for UI elements
  /// to consume. In this case, it will also update the SmartBand's date time
  /// based on the current date time.
  Future<void> processEvent(
    BluetoothBackendEvent event, {
    required SmartBandDeviceController controller,
  }) async {
    final sensorEventInfo = event.toSensorEventInfo();

    // If SB has been unassigned, don't do anything
    if (sensorEventInfo != null &&
        _sharedPreferencesService.getSbSerial() != null) {
      await _sensorEventInfoRepository.add(sensorEventInfo);
    }

    if (event.type == BluetoothBackendEventType.connectionStatusChanged) {
      await _onConnectionChanged(event, controller);
    } else if (event.type == BluetoothBackendEventType.deviceEventReceived) {
      await _onDeviceEvent(event, controller);
    }
  }

  Future<void> _onDeviceEvent(
    BluetoothBackendEvent event,
    SmartBandDeviceController controller,
  ) async {
    final eventData = event.asDeviceEvent();

    // acknowledge receipt of the event
    final response =
        await controller.acknowledgeEvent(eventTimestamp: eventData.timestamp);

    // not connected or something, so exit without trying to do more.
    if (response == null) return;

    if (response != EventAcknowledgementResponse.noMoreEventsPending) {
      // more to get, so request another
      await controller.requestNextPendingEvent();
    }
  }

  Future<void> _onConnectionChanged(
    BluetoothBackendEvent event,
    SmartBandDeviceController controller,
  ) async {
    final change = event.asConnectionChanged();
    if (change.status == ConnectionStatus.connected &&
        !change.isRestorationEvent) {
      await _onConnected(controller);
    }
  }

  Future<void> _onConnected(SmartBandDeviceController controller) async {
    // determine the firmware version we are using so we can set the
    // protocol to use. This must be done first so that all subsequent commands
    // execute in the correct way.
    final fw = await controller.getFirmwareVersion();
    if (fw == null) return;
    await _sharedPreferencesService.setSbFirmwareVersion(fw);

    final setCentralIssued =
        await controller.setCentralPlatform(platform: _centralPlatform);

    // didn't issue the command due to being disconnected or something
    if (!setCentralIssued) return;

    final serverTime = await _biometricRepository.getServerTime();
    if (serverTime?.utcDateTime != null) {
      final setDateTimeIssued =
          await controller.setDateTime(serverTime!.utcDateTime!);

      // didnt' issue date time request so cancel
      if (!setDateTimeIssued) return;
    }

    // always request next pending event on connected
    await controller.requestNextPendingEvent();
  }
}

import 'dart:async';
import 'package:bi_embark_l10n/l10n/app_localizations.dart';
import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_flutter_notifications/models/models.dart';
import 'package:embark/repositories/_app_repository_mixins/banner_message_repository.dart';
import 'package:embark/repositories/_app_repository_mixins/locale_stream_repository.dart';
import 'package:embark/services/push_notifications_services/_remote_notifications_service.dart';
import 'package:bi_flutter_login_widget/authentication/authentication_status_base.dart';
import 'package:embark/services/_shared_preferences_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ClosableParent extends Closable {
  @mustCallSuper
  @override
  FutureOr<void> close() {}

  @override
  bool get isClosed => throw UnimplementedError();
}

class AppRepository extends ClosableParent
    with BannerMessageRepository, LocaleStreamRepository {
  AppRepository(
    this._sharedPreferencesService,
    this._remoteNotificationsService,
    this._getLocale,
  ) {
    // when the app repo is setup,
    // initialize the app with whatever is stored,
    // which is either the system default or the user specified
    // language.
    setLocale(getStoredLocale());

    _pnForegroundSubscription = _remoteNotificationsService
        .foregroundNotifications
        .listen((remotePushedPayload) {
      final pushedPayload = remotePushedPayload.copyWith(
        title: remotePushedPayload.title?.trim(),
        message: remotePushedPayload.message?.trim(),
      );

      _foregroundMessageStreamController.sink.add(pushedPayload);
    });
  }

  final SharedPreferencesService _sharedPreferencesService;
  final RemoteNotificationsService _remoteNotificationsService;
  final Locale Function() _getLocale;

  static const defaultLanguageCode = 'en-US';
  static const turkishLanguageCode = 'tr';

  late StreamSubscription<PushPayload> _pnForegroundSubscription;

  // AppLifecycleState streams
  final _appLifecycleStateController =
      BiStreamController<AppLifecycleState>.broadcast();

  Sink<AppLifecycleState> get _appLifecycleStateSink =>
      _appLifecycleStateController.sink;

  Stream<AppLifecycleState> get appLifecycleStateStream =>
      _appLifecycleStateController.stream;

  // AuthenticationStatus streams
  final _authenticationStatusStreamController =
      BiStreamController<AuthenticationStatusBase>();

  Sink<AuthenticationStatusBase> get authenticationStatusSink =>
      _authenticationStatusStreamController.sink;

  Stream<AuthenticationStatusBase> get authenticationStatusStream =>
      _authenticationStatusStreamController.stream;

  /// A [StreamController] for whether or not to translate messages
  /// Must be a broadcast since the repository is alive for the entire app run
  /// while subscribers will come and go.
  final _translatemessagesStreamController =
      BiStreamController<bool>.broadcast();

  /// The [Stream] for whether or not to translate messages
  Stream<bool> get translateMessagesStream =>
      _translatemessagesStreamController.stream.asBroadcastStream();

  /// A [StreamController] for foreground push notifications as broadcast
  /// for multiple subscribers
  final BiStreamController<PushPayload> _foregroundMessageStreamController =
      BiStreamController<PushPayload>.broadcast();

  /// The [Stream] for foreground push notifications
  Stream<PushPayload> get foregroundNotificationsStream =>
      _foregroundMessageStreamController.stream;

  @visibleForTesting
  Sink<PushPayload> get foregroundNotificationsSink =>
      _foregroundMessageStreamController.sink;

  // locale shared preferences key
  final String localeKey = 'locale';

  // permissions shared prefereneces key
  final String _permissionsKey = 'permissions';

  // translate messages preferences key
  final String _translateMessagesKey = 'translateMessages';

  // show translation message preferences key
  final String _showTranslationMessageKey = 'showTranslationMessage';

  //Load state into the stream
  void setAppLifecycleState(AppLifecycleState state) =>
      _appLifecycleStateSink.add(state);

  bool get hasSelectedLanguage =>
      _sharedPreferencesService.containsKey(localeKey);

  Locale getStoredLocale() {
    final localeString = _sharedPreferencesService.getString(localeKey);

    return localeString == null ? _getLocale() : Locale(localeString);
  }

  Future<AppLocalizations> loadL10n() =>
      AppLocalizations.delegate.load(getStoredLocale());

  bool getAcceptedPermissions() {
    return _sharedPreferencesService.getBool(_permissionsKey, false);
  }

  Future<bool> setAcceptedPermissions(bool value) async {
    return await _sharedPreferencesService.setBool(_permissionsKey, value);
  }

  bool getTranslateMessages() {
    return _sharedPreferencesService.getBool(_translateMessagesKey, false);
  }

  int getStartingTabIndex() {
    return 2;
  }

  void setTranslateMessages(bool value) {
    _translatemessagesStreamController.sink.add(value);
  }

  Future<bool> storeTranslateMessages(bool value) async {
    return await _sharedPreferencesService.setBool(
      _translateMessagesKey,
      value,
    );
  }

  bool getShowTranslationMessage() {
    return _sharedPreferencesService.getBool(_showTranslationMessageKey, true);
  }

  Future<bool> setShowTranslationMessage(bool value) async {
    return await _sharedPreferencesService.setBool(
      _showTranslationMessageKey,
      value,
    );
  }

  bool containsTranslateMessagesKey() {
    return _sharedPreferencesService.containsKey(_translateMessagesKey);
  }

  bool currentLocaleIsEnglish() {
    final currentLocale = getStoredLocale();
    return currentLocale.languageCode.startsWith('en') == true;
  }

  @override
  Future<void> close() async {
    await super.close();
    await _pnForegroundSubscription.cancel();
    await _translatemessagesStreamController.close();
    await _authenticationStatusStreamController.close();
    await _foregroundMessageStreamController.close();
    await _appLifecycleStateController.close();
  }

  @override
  bool get isClosed => throw UnimplementedError();
}

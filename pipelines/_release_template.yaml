
parameters:
- name: buildFlavor
  type: string
- name: settingsKey
  type: string
- name: iosProvisioningProfile
  type: string
- name: iosCertFile
  type: string
- name: iosCertPassword
  type: string
- name: androidKeystoreFile
  type: string
- name: androidKeystorePassword
  type: string
- name: androidKeyAlias
  type: string
- name: androidKeyPassword
  type: string
- name: environment
  type: string
- name: buildId
  type: number
  default: 0
- name: dependsOnBuildId
  type: number
  default: -1
- name: skipIOSBuild
  type: boolean
  default: false
- name: additionalBuildSteps
  type: object
  default: []
- name: firebaseApplicationId
  type: string
- name: firebaseToken
  type: string
- name: firebaseTestGroups
  type: string
- name: changesNotSentForReview
  type: boolean
  default: false

stages:
  - stage: Build_${{ parameters.buildId }}
    ${{ if gt(parameters.dependsOnBuildId, -1) }}:
      dependsOn: Release_${{ parameters.dependsOnBuildId }}
    ${{ else }}:
      dependsOn: []
    displayName: Build ${{ upper(parameters.buildFlavor) }}
    variables:
      - group: Common ${{ parameters.settingsKey }} Settings
      - group: SmartLINK STS ${{ parameters.settingsKey }}
      - group: Embark Configs - ${{ parameters.settingsKey }}
      - ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/hotfix/') }}:
        - name: BI_BUILD_ENV
          value: "DEV HOTFIX - ${{ variables['Build.SourceBranchName'] }}"
    jobs:
    - template: flutter.yml@shared_pipeline
      parameters:
        flutterVersion: 3.27.1-stable
        errorOnLintWarnings: true
        skipStaticAnalysis: true
        skipIOSBuild: ${{ parameters.skipIOSBuild }}
        additionalBuildSteps: ${{ parameters.additionalBuildSteps }}
        buildFormat: 'aab'
        binaryArtifactDir: ${{ parameters.buildFlavor }}
        buildIdSuffix: ${{ parameters.buildId }}

        # Flutter specific things
        secureDotenvKeys:
          - BI_STS_CLIENT_ID
          - BI_STS_CLIENT_SECRET
          - BI_OKTA_USERNAME
          - BI_OKTA_PASSWORD

        # Android specific things
        minAndroidSdkVersion: 33
        buildFlavor: ${{ parameters.buildFlavor }}

        # ios Specific things
        cocopodsVersion: '1.14.3'
        
  - stage: Release_${{ parameters.buildId }}
    displayName: 'Release ${{ parameters.buildFlavor }}' 
    variables:
      - group: Common ${{ parameters.settingsKey }} Settings
      - ${{ if startsWith(variables['Build.SourceBranch'], 'refs/heads/hotfix/') }}:
        - name: BI_BUILD_ENV
          value: "DEV HOTFIX - ${{ variables['Build.SourceBranchName'] }}"
    jobs:
    - deployment: Deploy_Android #No spaces
      displayName: Deploy Android #Name displayed in UI
      environment: ${{ parameters.environment }}
      dependsOn: [] # To let it run in parallel
      strategy:
        runOnce:
          deploy:
            steps:
            - download: current
              artifact: android/${{ parameters.buildFlavor }}
            # Sign
            - template: flutter/tasks/android/sign-v1.yml@shared_pipeline
              parameters:
                aabDir: $(Pipeline.Workspace)/android/${{ parameters.buildFlavor }}
                apkDir: $(Pipeline.Workspace)/android/${{ parameters.buildFlavor }}
                keystoreFile: ${{ parameters.androidKeystoreFile }}
                keystorePassword: ${{ parameters.androidKeystorePassword }}
                keystoreKeyAlias: ${{ parameters.androidKeyAlias }}
                keystoreKeyPassword: ${{ parameters.androidKeyPassword }}
                minSdkVersion: 33
                buildFormat: 'aab'
            # Deploy via Google Play Console
            - template: common/tasks/google-play-deploy-v1.yml@shared_pipeline
              parameters:
                binaryFile: $(Pipeline.Workspace)/android/${{ parameters.buildFlavor }}/*-${{ lower(parameters.buildFlavor) }}-*.aab
                applicationId: 'com.biinc.mobile.client'
                releaseNotes: 'Release ${{ parameters.buildFlavor }}'
                changesNotSentForReview: ${{ parameters.changesNotSentForReview }}
                
            # Deploy via Firebase App Distribution
            - template: common/tasks/firebase-app-distribution-v1.yml@shared_pipeline
              parameters:
                pathToAab: $(Pipeline.Workspace)/android/${{ parameters.buildFlavor }}/*-${{ lower(parameters.buildFlavor) }}-*.apk
                firebaseApplicationId: ${{ parameters.firebaseApplicationId }}
                firebaseToken: ${{ parameters.firebaseToken }}
                firebaseTestGroups: ${{ parameters.firebaseTestGroups }}

    - ${{ if not(parameters.skipIOSBuild) }}:
      - deployment: Deploy_iOS #No spaces
        displayName: Deploy iOS #Name displayed in UI
        environment: ${{ parameters.environment }}
        # Remove this pool image in favor of global one once these are fixed/deployed
        # https://github.com/actions/runner-images/issues/10817
        pool:
          vmImage: 'macOS-14'
        dependsOn: [] # To let it run in parallel
        strategy:
          runOnce:
            deploy:
              steps:
              - checkout: self
                path: source
              - download: current
                artifact: iOS/${{ parameters.buildFlavor }}

              # Prepare to sign
              - template: flutter/tasks/ios/prepare-signing.yml@shared_pipeline
                parameters:
                  certificateFile: ${{ parameters.iosCertFile }}
                  certificatePassword: ${{ parameters.iosCertPassword }}
                  provisioningProfileName: ${{ parameters.iosProvisioningProfile }}

              # Sign
              - template: flutter/tasks/ios/sign.yml@shared_pipeline
                parameters:
                  archivePath: $(Pipeline.Workspace)/iOS/${{ parameters.buildFlavor }}/Runner.xcarchive
                  exportOptionsPlist: ios/prodBuild.plist
                  outputDir: $(Pipeline.Workspace)/iOS/${{ parameters.buildFlavor }}
                  
              # Deploy
              - template: common/tasks/apple-app-store-deploy-v1.yml@shared_pipeline
                parameters:
                  appIdentifier: 'com.biinc.mobile.client'
                  binaryFile: $(Pipeline.Workspace)/iOS/${{ parameters.buildFlavor }}/*.ipa

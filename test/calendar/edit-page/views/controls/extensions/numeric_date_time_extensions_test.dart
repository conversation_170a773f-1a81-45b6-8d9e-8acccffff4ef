import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_flutter_ui_test/helpers/_test_data_loader.dart';
import 'package:embark/calendar/edit-page/views/controls/extensions/_numeric_date_time_extensions.dart';
import 'package:embark/extensions/_date_time_extensions.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final testDataLoader = TestDataLoader();
  const String newCalendarItemRulePath =
      '../../../json/test-01.json';

  Future<CalendarRuleInfo> loadRuleFromFile(String name) async {
    final json = await testDataLoader.loadRelative(name);
    return CalendarRuleInfo.fromJson(json);
  }

  group('CalendarRuleInfoEntryExtensions', () {
    test('extractDurationMetadata returns correct metadata - initial state', () async {
      final ruleInfo = await loadRuleFromFile(newCalendarItemRulePath);
      final metadata = ruleInfo.extractDurationMetadata(withRespectTo: ruleInfo);

      expect(metadata.localStartDate, isNotNull);
      expect(metadata.localEndDate, isNull);
      expect(metadata.localMinDate, isNull);
      expect(metadata.localMaxDate, isNull);
      expect(metadata.minutesFromStartDate, isNull);
    });
    test('extractDurationMetadata returns correct metadata - with value', () async {
      const dateTime = '2022-01-01T00:00';
      final ruleInfo = await loadRuleFromFile(newCalendarItemRulePath);

      final withRespectTo = (await loadRuleFromFile(newCalendarItemRulePath))
          .copyWithValue(dateTime);
      final metadata = ruleInfo.extractDurationMetadata(withRespectTo: withRespectTo);

      expect(metadata.localStartDate.toApiDateTimeString(), dateTime);
    });
    test('extractDurationMetadata returns correct metadata - with min and max', () async {
      const dateTime = '2022-01-01T00:00';
      const minValue = 10;
      const maxValue = 10;
      final ruleInfo = (await loadRuleFromFile(newCalendarItemRulePath))
          .copyWith(
            value: minValue.toString(),
            minValue: minValue,
            maxValue: maxValue,
          );

      final withRespectTo = (await loadRuleFromFile(newCalendarItemRulePath))
          .copyWith(value: dateTime);
      final metadata = ruleInfo.extractDurationMetadata(withRespectTo: withRespectTo);

      expect(metadata.localMinDate, isNotNull);
      expect(metadata.localMaxDate, isNotNull);
      expect(metadata.minutesFromStartDate, isNotNull);

      expect(metadata.localStartDate, isNotNull);
      expect(metadata.localEndDate, isNotNull);
    });
  });
}
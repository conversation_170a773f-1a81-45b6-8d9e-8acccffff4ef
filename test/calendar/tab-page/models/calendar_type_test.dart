import 'package:embark/calendar/tab-page/models/calendar_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CalendarType', () {
    test('values', () {
      expect(CalendarType.values.length, 5);
    });

    test('fromInt', () {
      expect(CalendarType.fromInt(0), CalendarType.day);
      expect(CalendarType.fromInt(1), CalendarType.week);
      expect(CalendarType.fromInt(2), CalendarType.month);
      expect(CalendarType.fromInt(3), CalendarType.list);

      expect(CalendarType.fromInt(-1), CalendarType.unknown);
    });
  });
}
import 'dart:async';

import 'package:async/async.dart';
import 'package:bi_permissions/cubit/_bi_permission_request_handler_cubit.dart';
import 'package:bi_permissions/cubit/_bi_permission_request_handler_state.dart';
import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bi_permission.dart';
import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';

void main() {
  late BiPermissionRequestHandlerCubit cubit;
  late BiPermissionRequestRepository repository;
  late StreamQueue<BiPermissionRequestHandlerState> stateQueue;
  late FakePermissionHandlerPlatform permissionHandlerPlatform;

  setUp(() async {
    permissionHandlerPlatform = FakePermissionHandlerPlatform.init();
    repository = BiPermissionRequestRepository(
      bluetoothApi: MockBiBluetoothApi(),
    );
    cubit = BiPermissionRequestHandlerCubit(repository);
    stateQueue = StreamQueue(cubit.stream);
  });

  tearDown(() {
    debugDefaultTargetPlatformOverride = null;
  });

  test('default state is idle', () {
    expect(
      cubit.state,
      equals(
        const BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
      ),
    );
  });

  test('requests permission correctly', () async {
    bool requested = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      onShouldShowRationale: () => false,
      onRequest: (_) => requested = true,
    );

    unawaited(repository.requestPermission(permission));

    final emits = await stateQueue.take(1);
    expect(
      emits,
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
      ]),
    );
    expect(requested, isTrue);
  });

  test('emits correctly if need to show rationale', () async {
    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );

    bool requested = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationAlways,
      onShouldShowRationale: () => true,
      onRequest: (_) => requested = true,
    );

    unawaited(repository.requestPermission(permission));

    final emits = await stateQueue.take(2);
    expect(
      emits,
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.showingRationale,
          requestedPermission: permission,
        ),
      ]),
    );
    expect(requested, isFalse);
  });

  test('completes original request on resume', () async {
    bool granted = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      isGranted: () => granted,
      onRequest: (_) => granted = true,
    );

    final originalRequest = repository.requestPermission(permission);

    // idle -> requesting
    await stateQueue.take(1);

    // app onResume
    await cubit.onResume();

    // requesting -> idle
    await stateQueue.take(1);
    expect(await originalRequest, isTrue);
  });

  test('emits idle on resume', () async {
    bool granted = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      isGranted: () => granted,
      onRequest: (_) => granted = true,
    );

    unawaited(repository.requestPermission(permission));

    expect(
      await stateQueue.take(1),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
      ]),
    );

    await cubit.onResume();
    expect(
      await stateQueue.take(1),
      equals([
        const BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
      ]),
    );
  });

  test('completes original request on resume after rationale and requesting',
      () async {
    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );

    bool granted = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationAlways,
      isGranted: () => granted,
      onRequest: (_) => granted = true,
      onShouldShowRationale: () => true,
    );

    final originalRequest = repository.requestPermission(permission);

    // idle -> requesting
    expect(
      await stateQueue.take(1),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
      ]),
    );

    // requesting -> rationale
    expect(
      await stateQueue.take(1),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.showingRationale,
          requestedPermission: permission,
        ),
      ]),
    );
    // app onResume after rationale dialog closed
    await cubit.onResume();

    // rationale -> requesting
    expect(
      await stateQueue.take(1),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
      ]),
    );

    // app onResume after request
    await cubit.onResume();

    // requesting -> idle
    expect(
      await stateQueue.take(1),
      equals(const [
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
      ]),
    );
    expect(await originalRequest, isTrue);
  });

  test('requests next queued permission after first completes', () async {
    bool granted = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      isGranted: () => granted,
      onRequest: (_) {
        permissionHandlerPlatform.setStatus(
          Permission.locationWhenInUse,
          PermissionStatus.granted,
        );
        granted = true;
      },
      onShouldShowRationale: () => false,
    );

    bool secondGranted = false;
    final secondPermission = FakeBiPermission(
      type: BiPermissionType.locationAlways,
      isGranted: () => secondGranted,
      onRequest: (_) => secondGranted = true,
      onShouldShowRationale: () => true,
    );

    final originalRequest = repository.requestPermission(permission);
    final secondRequest = repository.requestPermission(secondPermission);

    // idle -> requesting
    expect(
      await stateQueue.take(1),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
      ]),
    );

    // app onResume
    await cubit.onResume();

    expect(
      await stateQueue.take(3),
      equals([
        const BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: secondPermission,
        ),
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.showingRationale,
          requestedPermission: secondPermission,
        ),
      ]),
    );
    await cubit.onResume();

    expect(
      await stateQueue.take(1),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: secondPermission,
        ),
      ]),
    );

    await cubit.onResume();
    expect(
      await stateQueue.take(1),
      equals(const [
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
      ]),
    );
    expect(await originalRequest, isTrue);
    expect(await secondRequest, isTrue);
  });

  test('emits correctly if rationale screen cancelled', () async {
    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );
    final permission = FakeBiPermission(
      type: BiPermissionType.locationAlways,
      onShouldShowRationale: () => true,
    );

    unawaited(repository.requestPermission(permission));

    expect(
      await stateQueue.take(2),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.showingRationale,
          requestedPermission: permission,
        ),
      ]),
    );
    await cubit.cancelledRationaleDialog();
    expect(
      await stateQueue.take(2),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
        const BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
      ]),
    );
  });

  test('updates state correctly when inactive', () async {
    await cubit.onInactive();

    expect(
      await stateQueue.take(1),
      equals(const [
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
          appInactive: true,
        ),
      ]),
    );
  });

  test('schedules inactive timer on iOS when locationAlways requested',
      () async {
    debugDefaultTargetPlatformOverride = TargetPlatform.iOS;

    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );

    const permission = FakeBiPermission(
      type: BiPermissionType.locationAlways,
    );

    unawaited(repository.requestPermission(permission));

    expect(
      await stateQueue.take(1),
      equals(const [
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
      ]),
    );

    final startTime = DateTime.now();
    // wait for the inactive timer to run
    final nextState = await stateQueue.take(1);

    // get the time that the inactive timer ran
    final endTime = DateTime.now();

    expect(
      nextState,
      equals([
        const BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
      ]),
    );
    // inactive timer is running for 1 second, so it should be close to that.
    expect(endTime.difference(startTime).inMilliseconds, closeTo(1000, 200));
  });

  test(
      'ignores inactive timer on iOS when locationAlways causes app to go inactive',
      () async {
    debugDefaultTargetPlatformOverride = TargetPlatform.iOS;

    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );

    final permission = FakeBiPermission(
      type: BiPermissionType.locationAlways,
      onRequest: (_) async {
        await cubit.onInactive();
        await cubit.onResume();
      },
    );

    unawaited(repository.requestPermission(permission));

    expect(
      await stateQueue.take(4),
      equals([
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
        ),
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
          appInactive: true,
        ),
        BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.requesting,
          requestedPermission: permission,
          appInactive: false,
        ),
        const BiPermissionRequestHandlerState(
          status: PermissionHandlerStatus.idle,
        ),
      ]),
    );
  });
}

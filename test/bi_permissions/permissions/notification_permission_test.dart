import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/permissions/_notification_permission.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';

void main() {
  const permission = NotificationPermission();
  late FakePermissionHandlerPlatform permissionPlatform;

  setUp(() {
    permissionPlatform = FakePermissionHandlerPlatform.init();
  });

  test('has correct type', () {
    expect(permission.type, equals(BiPermissionType.notification));
  });

  test('returns correct granted status', () async {
    permissionPlatform.setStatus(
      Permission.notification,
      PermissionStatus.granted,
    );
    expect(await permission.granted, isTrue);
  });
  test('permanent denied is true', () async {
    permissionPlatform.setStatus(
      Permission.notification,
      PermissionStatus.permanentlyDenied,
    );

    expect(await permission.permanentlyDenied, isTrue);
  });

  test('permanent denied is false', () async {
    permissionPlatform.setStatus(
      Permission.notification,
      PermissionStatus.denied,
    );

    expect(await permission.permanentlyDenied, isFalse);
  });
  test('should not show rationale', () async {
    expect(await permission.shouldShowRationale, isFalse);
  });
  test('requests permission correctly', () async {
    await permission.request();

    expect(
      permissionPlatform.permissionsRequested,
      equals({Permission.notification}),
    );
  });
}

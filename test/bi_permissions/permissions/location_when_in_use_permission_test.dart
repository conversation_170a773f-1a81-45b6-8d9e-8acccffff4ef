import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/permissions/_location_when_in_use_permission.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';

void main() {
  const permission = LocationWhenInUsePermission();
  late FakePermissionHandlerPlatform permissionPlatform;

  setUp(() {
    permissionPlatform = FakePermissionHandlerPlatform.init();
  });

  test('has correct type', () {
    expect(permission.type, equals(BiPermissionType.locationWhenInUse));
  });

  test('returns correct granted status', () async {
    permissionPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );
    expect(await permission.granted, isTrue);
  });

  test('permanent denied is true', () async {
    permissionPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.permanentlyDenied,
    );

    expect(await permission.permanentlyDenied, isTrue);
  });

  test('permanent denied is false', () async {
    permissionPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.denied,
    );

    expect(await permission.permanentlyDenied, isFalse);
  });

  test('does not show rationale', () async {
    expect(await permission.shouldShowRationale, isFalse);
  });

  test('requests permission correctly', () async {
    await permission.request();

    expect(
      permissionPlatform.permissionsRequested,
      equals({Permission.locationWhenInUse}),
    );
  });
}

import 'package:bi_permissions/bi_permissions.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../unit_test.mocks.dart';

class _DefaultPermission extends BiPermission {
  const _DefaultPermission();

  @override
  Future<bool> get granted => throw UnimplementedError();

  @override
  Future<void> request() {
    throw UnimplementedError();
  }

  @override
  BiPermissionType get type => BiPermissionType.unknown;

  @override
  Future<bool> get permanentlyDenied async => false;
}

void main() {
  late BiSupportedPermissions permissions;
  setUp(() {
    permissions = BiSupportedPermissions(bluetoothApi: MockBiBluetoothApi());
  });

  test('has correct defaults', () async {
    const permission = _DefaultPermission();
    expect(await permission.shouldShowRationale, isFalse);
    expect(permission.props, equals([BiPermissionType.unknown]));
  });

  test('creates correct bluetooth permission', () {
    expect(permissions.bluetooth, isA<BluetoothPermission>());
  });

  test('creates correct notification permission', () {
    expect(permissions.notification, isA<NotificationPermission>());
  });
  test('creates correct location when in use permission', () {
    expect(permissions.locationWhenInUse, isA<LocationWhenInUsePermission>());
  });
  test('creates correct location always permission', () {
    expect(permissions.locationAlways, isA<LocationAlwaysPermission>());
  });
}

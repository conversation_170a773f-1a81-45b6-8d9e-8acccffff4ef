import 'package:bi_flutter_bluetooth_platform_api/plugin/plugin.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:embark/starter.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../unit_test_helpers/fakes/fake_bi_bluetooth_api.dart';

void main() {
  late BluetoothPermission permission;
  late FakeBiBluetoothApi bluetoothPlatform;

  setUp(() {
    getIt.registerSingleton(BiBluetoothApi());
    bluetoothPlatform = FakeBiBluetoothApi.init();
    permission = BluetoothPermission(bluetoothPlatform);
  });

  tearDown(() {
    getIt.reset();
  });

  test('has correct type', () {
    expect(permission.type, equals(BiPermissionType.bluetooth));
  });

  test('returns correct granted status', () async {
    bluetoothPlatform.setPermissionStatus(BluetoothPermissionStatus.granted);
    expect(await permission.granted, isTrue);
  });

  test('permanent denied is false', () async {
    expect(await permission.permanentlyDenied, isFalse);
  });

  test('should not show rationale', () async {
    expect(await permission.shouldShowRationale, isFalse);
  });

  test('requests permission correctly', () async {
    bool requested = false;
    bluetoothPlatform.onRequestPermission = () {
      requested = true;
      return BluetoothPermissionStatus.granted;
    };
    await permission.request();

    expect(requested, isTrue);
  });
}

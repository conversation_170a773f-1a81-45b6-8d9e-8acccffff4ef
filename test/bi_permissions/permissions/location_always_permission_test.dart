import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/permissions/_location_always_permission.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';

void main() {
  const permission = LocationAlwaysPermission();
  late FakePermissionHandlerPlatform permissionPlatform;

  setUp(() {
    permissionPlatform = FakePermissionHandlerPlatform.init();
  });

  test('has correct type', () {
    expect(permission.type, equals(BiPermissionType.locationAlways));
  });

  test('should show rationale if necessary', () async {
    permissionPlatform.setShouldShowRationale(Permission.locationAlways, true);
    expect(await permission.shouldShowRationale, isTrue);
  });

  test('should not show', () async {
    permissionPlatform.setShouldShowRationale(Permission.locationAlways, false);
    expect(await permission.shouldShowRationale, isFalse);
  });

  test('permanent denied is true', () async {
    permissionPlatform.setStatus(
      Permission.locationAlways,
      PermissionStatus.permanentlyDenied,
    );

    expect(await permission.permanentlyDenied, isTrue);
  });

  test('permanent denied is false', () async {
    permissionPlatform.setStatus(
      Permission.locationAlways,
      PermissionStatus.denied,
    );

    expect(await permission.permanentlyDenied, isFalse);
  });

  test('returns correct granted status', () async {
    permissionPlatform.setStatus(
      Permission.locationAlways,
      PermissionStatus.granted,
    );
    expect(await permission.granted, isTrue);
  });

  test('requests permission correctly', () async {
    await permission.request();

    expect(
      permissionPlatform.permissionsRequested,
      equals({Permission.locationAlways}),
    );
  });
}

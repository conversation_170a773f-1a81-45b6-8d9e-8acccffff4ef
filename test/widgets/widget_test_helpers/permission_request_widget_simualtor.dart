import 'dart:async';

import 'package:bi_flutter_bluetooth_platform_api/plugin/plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../unit_test_helpers/fakes/fake_bi_bluetooth_api.dart';
import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';

class PermissionRequestDialogSimualtor {
  Completer<BluetoothPermissionStatus>? _bluetoothDialog;
  Completer<PermissionStatus>? _permissionStatusDialog;

  Permission? _requestingPermission;
  Permission? get requestingPermission => _requestingPermission;

  final WidgetTester tester;

  bool get bluetoothDialogOpen => _bluetoothDialog != null;
  bool get permissionHandlerDialogOpen => _permissionStatusDialog != null;

  PermissionRequestDialogSimualtor({
    required this.tester,
  });

  void closeBluetoothDialog(BluetoothPermissionStatus result) {
    _bluetoothDialog?.complete(result);
    _bluetoothDialog = null;
  }

  void closePermissionHandlerDialog(PermissionStatus result) {
    _permissionStatusDialog?.complete(result);
    _permissionStatusDialog = null;
  }

  void enableBluetoothPermissionDialogSimulation(
    FakeBiBluetoothApi bluetoothApi,
  ) {
    bluetoothApi.onRequestPermission = () async {
      _bluetoothDialog = Completer<BluetoothPermissionStatus>();

      // go inactive since the dialog is showing
      tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.inactive);

      // wait for the dialog to close.
      final result = await _bluetoothDialog!.future;
      _bluetoothDialog = null;
      bluetoothApi.setPermissionStatus(result);

      // resume the app
      tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.resumed);

      return result;
    };
  }

  void enablePermissionHandlerDialogSimulation(
    FakePermissionHandlerPlatform permissionHandlerPlatform,
  ) {
    permissionHandlerPlatform.onRequestPermission = (permission) async {
      _permissionStatusDialog = Completer<PermissionStatus>();
      _requestingPermission = permission;

      // go inactive since the dialog is showing
      tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.inactive);

      // wait for the dialog to close.
      final result = await _permissionStatusDialog!.future;
      _permissionStatusDialog = null;
      permissionHandlerPlatform.setStatus(permission, result);

      // resume the app
      tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.resumed);

      return result;
    };
  }
}

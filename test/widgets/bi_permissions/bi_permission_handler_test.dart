import 'dart:async';

import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bi_permission.dart';

void main() {
  late BiPermissionRequestRepository repo;
  late AppLocalizations l10n;

  Future<void> loadTest(
    WidgetTester tester, {
    required BiPermission permission,
  }) async {
    await tester.load(
      widget: BiPermissionRequestProvider(
        create: (_) => repo,
        buildRationaleTitle: (context, permission) =>
            permission.type.index.toString(),
        buildRationale: (context, permission) => permission.type.name,
        cancelButtonText: l10n.cancel,
        continueButtonText: l10n.continueCommon,
        child: Center(
          child: BiButton(
            text: 'run',
            onTap: () async {
              await repo.requestPermission(permission);
            },
          ),
        ),
      ),
    );
  }

  Future<void> startTest(WidgetTester tester) async {
    await tester.tap(find.text('run'));
    await tester.pumpAndSettle();
  }

  Future<void> tapContinueOnRationaleDialog(WidgetTester tester) async {
    await tester.tap(find.text(l10n.continueCommon));
    await tester.pumpAndSettle();
  }

  Future<void> tapCancelOnRationaleDialog(WidgetTester tester) async {
    await tester.tap(find.text(l10n.cancel));
    await tester.pumpAndSettle();
  }

  setUp(() {
    repo = BiPermissionRequestRepository(
      bluetoothApi: MockBiBluetoothApi(),
    );
    l10n = AppLocalizationsEn();
    getIt.registerSingleton(repo);
  });

  tearDown(() {
    getIt.reset();
  });

  testWidgets('shows rationale dialog if needed', (tester) async {
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      onShouldShowRationale: () => true,
      isGranted: () => false,
    );

    await loadTest(tester, permission: permission);

    await startTest(tester);
    expect(find.byType(BiConfirmationDialog), findsOneWidget);
  });

  testWidgets('requests permission after rationale is accepted',
      (tester) async {
    bool requested = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      onShouldShowRationale: () => true,
      isGranted: () => false,
      onRequest: (_) => requested = true,
    );

    await loadTest(tester, permission: permission);

    await startTest(tester);
    await tapContinueOnRationaleDialog(tester);

    expect(requested, isTrue);
    expect(find.byType(BiConfirmationDialog), findsNothing);
  });

  testWidgets('does not request permission after rationale is cancelled',
      (tester) async {
    bool requested = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      onShouldShowRationale: () => true,
      isGranted: () => false,
      onRequest: (_) => requested = true,
    );

    await loadTest(tester, permission: permission);

    await startTest(tester);
    await tapCancelOnRationaleDialog(tester);

    expect(requested, isFalse);
    expect(find.byType(BiConfirmationDialog), findsNothing);
  });

  testWidgets('requests permission if no rationale needed', (tester) async {
    bool requested = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      onShouldShowRationale: () => false,
      isGranted: () => false,
      onRequest: (_) => requested = true,
    );

    await loadTest(tester, permission: permission);

    await startTest(tester);

    expect(requested, isTrue);
    expect(find.byType(BiConfirmationDialog), findsNothing);
  });

  testWidgets('handles queue correctly', (tester) async {
    bool requested = false;
    final permission = FakeBiPermission(
      type: BiPermissionType.notification,
      onShouldShowRationale: () => false,
      isGranted: () => requested,
      onRequest: (self) async {
        requested = true;
        await repo.permissionRequestComplete(self, true);
      },
    );

    bool secondRequested = false;
    final secondPermission = FakeBiPermission(
      type: BiPermissionType.locationWhenInUse,
      onShouldShowRationale: () => true,
      isGranted: () => secondRequested,
      onRequest: (_) => secondRequested = true,
    );

    await loadTest(tester, permission: permission);

    unawaited(repo.requestPermission(permission));
    unawaited(repo.requestPermission(secondPermission));

    await tester.pumpAndSettle();

    // first permission should have been requested, and second one should be at
    // the rationale dialog
    expect(requested, isTrue);
    expect(secondRequested, isFalse);

    expect(find.byType(BiConfirmationDialog), findsOneWidget);

    await tapContinueOnRationaleDialog(tester);

    expect(secondRequested, isTrue);
    expect(find.byType(BiConfirmationDialog), findsNothing);
  });

  testWidgets('uses correct rationale dialog based on permission',
      (tester) async {
    final notificationPermission = FakeBiPermission(
      type: BiPermissionType.notification,
      onShouldShowRationale: () => true,
      isGranted: () => false,
    );

    await loadTest(tester, permission: notificationPermission);

    await startTest(tester);

    final dialogFinder = find.byType(BiConfirmationDialog);
    final dialogWidget = tester.widget<BiConfirmationDialog>(dialogFinder);

    expect(
      dialogWidget.title,
      equals(BiPermissionType.notification.index.toString()),
    );
    expect(dialogWidget.subTitle, equals(BiPermissionType.notification.name));
    expect(dialogWidget.showNegativeButton, isTrue);
    expect(dialogWidget.negButtonTitle, equals(l10n.cancel));
    expect(dialogWidget.posButtonTitle, equals(l10n.continueCommon));
  });
}

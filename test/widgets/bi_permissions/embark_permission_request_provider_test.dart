import 'dart:async';

import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_permissions/permissions/permissions.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:embark/bi_permissions/_embark_permission_request_provider.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bi_permission.dart';
import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';
import '../widget_test_helpers/permission_request_widget_simualtor.dart';

void main() {
  late FakePermissionHandlerPlatform permissionHandlerPlatform;
  late AppLocalizations l10n;
  late BiPermissionRequestRepository requestRepository;
  late PermissionRequestDialogSimualtor dialogSimualtor;

  setUp(() {
    permissionHandlerPlatform = FakePermissionHandlerPlatform.init();
    requestRepository = BiPermissionRequestRepository(
      bluetoothApi: MockBiBluetoothApi(),
    );
    l10n = AppLocalizationsEn();

    getIt.registerSingleton(requestRepository);
  });

  tearDown(() {
    getIt.reset();
  });

  Future<void> loadTest(WidgetTester tester) async {
    dialogSimualtor = PermissionRequestDialogSimualtor(tester: tester);
    dialogSimualtor.enablePermissionHandlerDialogSimulation(
      permissionHandlerPlatform,
    );

    await tester.load(
      widget: const BiEmbarkPermissionRequestProvider(
        child: Text('child'),
      ),
    );
  }

  testWidgets('renders correct text for location always rationale',
      (tester) async {
    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );
    permissionHandlerPlatform.setShouldShowRationale(
      Permission.locationAlways,
      true,
    );
    await loadTest(tester);

    unawaited(
      requestRepository.requestPermission(
        requestRepository.supportedPermissions.locationAlways,
      ),
    );
    await tester.pumpAndSettle();

    final dialog = tester.widget<BiConfirmationDialog>(
      find.byType(BiConfirmationDialog),
    );

    expect(dialog.title, equals(l10n.permissionRequired));
    expect(dialog.subTitle, equals(l10n.locationAlwaysPermissionRationale));
  });

  testWidgets('renders correct text for notification rationale',
      (tester) async {
    permissionHandlerPlatform.setShouldShowRationale(
      Permission.notification,
      true,
    );
    await loadTest(tester);

    unawaited(
      requestRepository.requestPermission(
        requestRepository.supportedPermissions.notification,
      ),
    );
    await tester.pumpAndSettle();

    final dialog = tester.widget<BiConfirmationDialog>(
      find.byType(BiConfirmationDialog),
    );

    expect(dialog.title, equals(l10n.permissionRequired));
    expect(dialog.subTitle, equals(l10n.notificationPermissionRationale));
  });

  testWidgets('renders correct text for unknown permission rationale',
      (tester) async {
    await loadTest(tester);

    final permission = FakeBiPermission(
      type: BiPermissionType.unknown,
      onShouldShowRationale: () => true,
    );

    unawaited(
      requestRepository.requestPermission(
        permission,
      ),
    );
    await tester.pumpAndSettle();

    final dialog = tester.widget<BiConfirmationDialog>(
      find.byType(BiConfirmationDialog),
    );

    expect(dialog.title, equals(l10n.permissionRequired));
    expect(dialog.subTitle, equals(l10n.genericPermissionRationale));
  });
}

import 'package:embark/video_conference/views/_network_quality_indicator.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:twilio_programmable_video/twilio_programmable_video.dart';

void main() {
  group('BiEmbarkCalendarForm', () {
    testGoldens('renders correctly', (tester) async {
      const size = Size(52, 268);
      const defaultQuality = NetworkQualityIndicator();
      const excellentQuality = NetworkQualityIndicator(networkQualityLevel: NetworkQualityLevel.NETWORK_QUALITY_LEVEL_THREE);
      final builder = GoldenBuilder.column()
        ..addScenario('default', defaultQuality)
        ..addScenario('with network quality', excellentQuality);

      await tester.pumpWidgetBuilder(builder.build(), surfaceSize: size);
      await screenMatchesGolden(tester, 'network_quality_indicator');
    });
    testGoldens('network quality renders correctly for all levels',
        (tester) async {
      const size = Size(518, 443);
      final builder = GoldenBuilder.column();
      for (final networkQualityLevel in NetworkQualityLevel.values) {
        final widget = buildWidget(
          networkQualityLevel: networkQualityLevel,
          showFromNetworkQualityLevelAndBelow:
              NetworkQualityLevel.NETWORK_QUALITY_LEVEL_FIVE,
        );
        builder.addScenario(
          'network quality level ${networkQualityLevel.name}',
          widget,
        );
      }

      await tester.pumpWidgetBuilder(builder.build(), surfaceSize: size);
      await screenMatchesGolden(
        tester,
        'network_quality_indicator_show_all_levels',
      );
    });
    testGoldens('network quality renders correctly for lowest level',
        (tester) async {
      const size = Size(518, 443);
      final builder = GoldenBuilder.column();
      for (final networkQualityLevel in NetworkQualityLevel.values) {
        final widget = buildWidget(
          networkQualityLevel: networkQualityLevel,
          showFromNetworkQualityLevelAndBelow:
              NetworkQualityLevel.NETWORK_QUALITY_LEVEL_ZERO,
        );
        builder.addScenario(
          'network quality level ${networkQualityLevel.name}',
          widget,
        );
      }

      await tester.pumpWidgetBuilder(builder.build(), surfaceSize: size);
      await screenMatchesGolden(
        tester,
        'network_quality_indicator_show_lowest_levels',
      );
    });
  });
}

Widget buildWidget({
  required NetworkQualityLevel networkQualityLevel,
  required NetworkQualityLevel showFromNetworkQualityLevelAndBelow,
  Stream<NetworkQualityLevelChangedEvent>? onNetworkQualityChanged,
  NetworkQualityIndicatorPosition? networkQualityIndicatorPosition,
}) {
  return NetworkQualityIndicator(
    networkQualityLevel: networkQualityLevel,
    showFromNetworkQualityLevelAndBelow: showFromNetworkQualityLevelAndBelow,
    onNetworkQualityChanged: onNetworkQualityChanged,
    networkQualityIndicatorPosition: networkQualityIndicatorPosition,
  );
}
import 'package:embark/video_conference/views/_clipped_video.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';

void main() {
  group('BiEmbarkCalendarForm', () {
    testGoldens('renders correctly', (tester) async {
      const size = Size(100, 100);
      final widget = ClippedVideo(
        width: size.width,
        height: size.height,
        child: const Text('child'),
      );
      await tester.pumpWidgetBuilder(widget, surfaceSize: size);
      await screenMatchesGolden(tester, 'clipped_video');
    });
  });
}
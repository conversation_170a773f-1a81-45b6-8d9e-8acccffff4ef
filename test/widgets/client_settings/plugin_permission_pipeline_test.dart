import 'package:bi_embark_l10n/bi_embark_l10n.dart';
import 'package:bi_embark_l10n/l10n/app_localizations_en.dart';
import 'package:bi_flutter_bluetooth_platform_api/plugin/_bi_bluetooth_api.dart';
import 'package:bi_flutter_login_widget/bi_login_widget.dart';
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_permissions/bi_permissions.dart';
import 'package:embark/bi_permissions/_embark_permission_request_provider.dart';
import 'package:embark/client_settings/views/_client_setting_plugin_permission_pipeline.dart';
import 'package:embark/denied_permissions/_blocking_permission_page_factory.dart';
import 'package:embark/denied_permissions/bluetooth/_denied_bluetooth_permission_page.dart';
import 'package:embark/repositories/repositories.dart';
import 'package:embark/services/services.dart';
import 'package:embark/starter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';

import '../../tester_extensions.dart';
import '../../unit_test.mocks.dart';
import '../../unit_test_helpers/fakes/fake_bi_bluetooth_api.dart';
import '../../unit_test_helpers/fakes/fake_permission_handler_platform.dart';
import '../widget_test_helpers/permission_request_widget_simualtor.dart';

final Finder _childFinder = find.text('child');
final Finder _blockingBluetoothPageFinder =
    find.byType(BluetoothPermissionDeniedPage);
const _emptyClientSettings = GetClientSettingsResponse(
  preferredLanguage: PreferredLanguageResponse(
    cultureCode: 'cultureCode',
    cultureName: 'cultureName',
    languageCodeId: 1,
    languageId: 1,
  ),
);

void main() {
  late MockClientSettingsRepository clientSettingsRepository;
  late ValueNotifier<GetClientSettingsResponse?> clientSettings;
  late BiPermissionRequestRepository permissionRequestRepository;
  late FakeBiBluetoothApi bluetoothApi;
  late MockEmbarkPermissionPreferences permissionPreferences;
  late FakePermissionHandlerPlatform permissionHandlerPlatform;
  late AppLocalizations l10n;
  PermissionRequestDialogSimualtor? permissionDialogSimulator;

  final trackingPlugin = PluginResponse(type: PluginType.tracking.value);
  final smartBandPlugin = PluginResponse(
    type: PluginType.smartband.value,
    extraInfo: {
      'serialNumber': '1234567',
    },
  );

  GetClientSettingsResponse initializeSettings() {
    return clientSettings.value ?? _emptyClientSettings;
  }

  void addPlugins(List<PluginResponse> plugin) {
    final currentSettings = initializeSettings();
    clientSettings.value = currentSettings.copyWith(
      plugins: [
        ...currentSettings.plugins ?? [],
        ...plugin,
      ],
    );
  }

  void addPlugin(PluginResponse plugin) {
    addPlugins([plugin]);
  }

  setUp(() {
    l10n = AppLocalizationsEn();
    clientSettings = ValueNotifier(null);
    clientSettingsRepository = MockClientSettingsRepository();
    bluetoothApi = FakeBiBluetoothApi.init();
    permissionPreferences = MockEmbarkPermissionPreferences();
    permissionHandlerPlatform = FakePermissionHandlerPlatform.init();

    permissionRequestRepository = BiPermissionRequestRepository(
      bluetoothApi: bluetoothApi,
    );

    when(clientSettingsRepository.clientSettingsListener)
        .thenReturn(clientSettings);

    when(permissionPreferences.showBlockingPage(any)).thenReturn(true);

    getIt.registerSingleton<ClientSettingsRepository>(clientSettingsRepository);
    getIt.registerSingleton<BiPermissionRequestRepository>(
      permissionRequestRepository,
    );
    getIt.registerSingleton<EmbarkPermissionPreferences>(permissionPreferences);
  });

  tearDown(() {
    getIt.reset();
    permissionDialogSimulator = null;
  });

  Future<void> loadTest(WidgetTester tester) async {
    await tester.load(
      widget: const BiEmbarkPermissionRequestProvider(
        child: PluginPermissionPipeline(
          blockedPageBuilder: createBlockingPermissionPage,
          child: Text('child'),
        ),
      ),
    );
  }

  void enableBluetoothPermissionDialogSimulation(WidgetTester tester) {
    permissionDialogSimulator = permissionDialogSimulator ??
        PermissionRequestDialogSimualtor(
          tester: tester,
        );

    permissionDialogSimulator!.enableBluetoothPermissionDialogSimulation(
      bluetoothApi,
    );
  }

  void enablePermissionHandlerDialogSimulation(WidgetTester tester) {
    permissionDialogSimulator = permissionDialogSimulator ??
        PermissionRequestDialogSimualtor(
          tester: tester,
        );

    permissionDialogSimulator!.enablePermissionHandlerDialogSimulation(
      permissionHandlerPlatform,
    );
  }

  Future<void> leaveAppAndResume(WidgetTester tester) async {
    tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.inactive);
    await tester.pumpAndSettle();

    tester.binding.handleAppLifecycleStateChanged(AppLifecycleState.resumed);
    await tester.pumpAndSettle();
  }

  void grantLocationWhenInUsePermission() {
    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );
  }

  group('on load', () {
    testWidgets('shows child if no client settings', (tester) async {
      await loadTest(tester);
      expect(_childFinder, findsOneWidget);
    });
    testWidgets('shows child if no plugins', (tester) async {
      initializeSettings();

      await loadTest(tester);

      expect(_childFinder, findsOneWidget);
    });

    group('smartband plugin', () {
      setUp(() {
        addPlugin(smartBandPlugin);
      });
      testWidgets('hides child if bluetooth permission needed', (tester) async {
        await loadTest(tester);

        expect(_childFinder, findsNothing);
      });
      testWidgets('requests bluetooth permission then shows child on granted',
          (tester) async {
        enableBluetoothPermissionDialogSimulation(tester);

        await loadTest(tester);

        // not null once simulated dialog is shown
        expect(permissionDialogSimulator?.bluetoothDialogOpen, isNotNull);

        // grant permission
        permissionDialogSimulator!.closeBluetoothDialog(
          BluetoothPermissionStatus.granted,
        );

        await tester.pumpAndSettle();
        expect(_childFinder, findsOneWidget);
      });
      testWidgets('shows blocking ui page if first permission is denied',
          (tester) async {
        enableBluetoothPermissionDialogSimulation(tester);

        await loadTest(tester);

        // deny permission
        permissionDialogSimulator!.closeBluetoothDialog(
          BluetoothPermissionStatus.denied,
        );

        await tester.pumpAndSettle();
        expect(_blockingBluetoothPageFinder, findsOneWidget);
      });
      testWidgets('stays on blocking page if returned to app and still denied',
          (tester) async {
        enableBluetoothPermissionDialogSimulation(tester);

        await loadTest(tester);

        // deny permission
        permissionDialogSimulator!.closeBluetoothDialog(
          BluetoothPermissionStatus.denied,
        );

        await tester.pumpAndSettle();

        await leaveAppAndResume(tester);

        expect(_blockingBluetoothPageFinder, findsOneWidget);
      });
      testWidgets('sets hideBlockingPage flag after first request',
          (tester) async {
        enableBluetoothPermissionDialogSimulation(tester);

        await loadTest(tester);

        verifyNever(
          permissionPreferences.hideBlockingPage(
            BiPermissionType.bluetooth,
            any,
          ),
        );

        // deny permission
        permissionDialogSimulator!.closeBluetoothDialog(
          BluetoothPermissionStatus.denied,
        );
        await tester.pumpAndSettle();

        verify(
          permissionPreferences.hideBlockingPage(
            BiPermissionType.bluetooth,
            true,
          ),
        ).called(1);
      });
    });

    group('tracking plugin', () {
      setUp(() {
        addPlugin(trackingPlugin);
      });

      testWidgets('requests location when in use', (tester) async {
        await loadTest(tester);

        expect(
          permissionHandlerPlatform.permissionsRequested,
          equals({
            Permission.locationWhenInUse,
          }),
        );
      });

      testWidgets('requests location when in use then always', (tester) async {
        enablePermissionHandlerDialogSimulation(tester);

        await loadTest(tester);

        expect(
          permissionHandlerPlatform.permissionsRequested,
          equals({
            Permission.locationWhenInUse,
          }),
        );
        permissionHandlerPlatform.permissionsRequested.clear();

        // location when in use permission
        expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isTrue);
        permissionDialogSimulator!.closePermissionHandlerDialog(
          PermissionStatus.granted,
        );

        // wait for next location always dialog
        await tester.pumpAndSettle();
        expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isTrue);
        expect(
          permissionDialogSimulator!.requestingPermission,
          Permission.locationAlways,
        );

        expect(
          permissionHandlerPlatform.permissionsRequested,
          equals({
            Permission.locationAlways,
          }),
        );
      });

      testWidgets('hides child if location always permission needed',
          (tester) async {
        grantLocationWhenInUsePermission();
        await loadTest(tester);

        expect(_childFinder, findsNothing);
      });

      testWidgets(
          'shows location always permission rationale and does not request permission',
          (tester) async {
        grantLocationWhenInUsePermission();

        permissionHandlerPlatform.setShouldShowRationale(
          Permission.locationAlways,
          true,
        );

        await loadTest(tester);
        await tester.pumpAndSettle();

        // if not empty, it means we actually requested the settings page which we
        // don't expect here
        expect(permissionHandlerPlatform.permissionsRequested, isEmpty);
        expect(find.byType(BiConfirmationDialog), findsOneWidget);
      });
      testWidgets('requests location always permission after rationale',
          (tester) async {
        grantLocationWhenInUsePermission();

        enablePermissionHandlerDialogSimulation(tester);

        permissionHandlerPlatform.setShouldShowRationale(
          Permission.locationAlways,
          true,
        );

        await loadTest(tester);
        await tester.pumpAndSettle();

        expect(find.byType(BiConfirmationDialog), findsOneWidget);

        // tap continue
        await tester.tap(find.text(l10n.continueCommon));
        await tester.pumpAndSettle();

        // we should now be seeing the "Settings" page (Android) or a secondary dialog (iOS)
        expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isTrue);
        expect(
          permissionHandlerPlatform.permissionsRequested,
          equals({Permission.locationAlways}),
        );
      });
      testWidgets(
          'requests location always permission after rationale then shows child',
          (tester) async {
        grantLocationWhenInUsePermission();
        enablePermissionHandlerDialogSimulation(tester);

        permissionHandlerPlatform.setShouldShowRationale(
          Permission.locationAlways,
          true,
        );

        await loadTest(tester);
        await tester.pumpAndSettle();

        // tap continue
        await tester.tap(find.text(l10n.continueCommon));
        await tester.pumpAndSettle();

        // we should now be seeing the "Settings" page (Android) or a secondary dialog (iOS)
        permissionDialogSimulator!.closePermissionHandlerDialog(
          PermissionStatus.granted,
        );
        await tester.pumpAndSettle();
        expect(_childFinder, findsOneWidget);
      });

      testWidgets('sets hide blocking permissions page flag after request',
          (tester) async {
        grantLocationWhenInUsePermission();
        enablePermissionHandlerDialogSimulation(tester);

        permissionHandlerPlatform.setShouldShowRationale(
          Permission.locationAlways,
          true,
        );

        await loadTest(tester);
        await tester.pumpAndSettle();

        verifyNever(
          permissionPreferences.hideBlockingPage(
            BiPermissionType.locationAlways,
            any,
          ),
        );

        // tap continue on the rationale dialog
        await tester.tap(find.text(l10n.continueCommon));
        await tester.pumpAndSettle();

        // deny permission
        permissionDialogSimulator!.closePermissionHandlerDialog(
          PermissionStatus.granted,
        );
        await tester.pumpAndSettle();

        verify(
          permissionPreferences.hideBlockingPage(
            BiPermissionType.locationAlways,
            true,
          ),
        ).called(1);
      });
    });
  });

  testWidgets('checks tracking permission on changes to client settings',
      (tester) async {
    enablePermissionHandlerDialogSimulation(tester);

    await loadTest(tester);
    // nothing should be shown yet since no plugins added
    expect(permissionDialogSimulator!.bluetoothDialogOpen, isFalse);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isFalse);

    addPlugin(trackingPlugin);
    await tester.pumpAndSettle();

    expect(permissionDialogSimulator!.bluetoothDialogOpen, isFalse);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isTrue);
    expect(
      permissionHandlerPlatform.permissionsRequested,
      equals({
        Permission.locationWhenInUse,
      }),
    );
  });
  testWidgets('checks bluetooth permissions on changes to client settings',
      (tester) async {
    enableBluetoothPermissionDialogSimulation(tester);

    await loadTest(tester);
    // nothing should be shown yet since no plugins added
    expect(permissionDialogSimulator!.bluetoothDialogOpen, isFalse);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isFalse);

    addPlugin(smartBandPlugin);
    await tester.pumpAndSettle();

    expect(permissionDialogSimulator!.bluetoothDialogOpen, isTrue);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isFalse);
    expect(permissionHandlerPlatform.permissionsRequested, isEmpty);
  });

  testWidgets('checks correct plugins when added all at once', (tester) async {
    enableBluetoothPermissionDialogSimulation(tester);
    enablePermissionHandlerDialogSimulation(tester);

    await loadTest(tester);

    // nothing should be shown yet since no plugins added
    expect(permissionDialogSimulator!.bluetoothDialogOpen, isFalse);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isFalse);
    expect(_childFinder, findsOneWidget);

    // add plugins before triggering any dialogs
    addPlugins([trackingPlugin, smartBandPlugin]);

    // trigger dialog
    await tester.pumpAndSettle();

    // permissions show in plugin list order, so show tracking first
    expect(permissionDialogSimulator!.bluetoothDialogOpen, isFalse);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isNotNull);
    expect(
      permissionHandlerPlatform.permissionsRequested,
      equals({
        Permission.locationWhenInUse,
      }),
    );
    // since we loaded the widget with no permissions, we should have rendered
    // the child widget, and we shouldn't hide it again until permissions change
    // or some are requested.
    expect(_childFinder, findsOneWidget);

    // grant the location when in use permission
    permissionDialogSimulator!.closePermissionHandlerDialog(
      PermissionStatus.granted,
    );
    await tester.pumpAndSettle();

    // now should be showing location always
    expect(permissionDialogSimulator!.bluetoothDialogOpen, isFalse);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isTrue);
    expect(
      permissionHandlerPlatform.permissionsRequested,
      equals({Permission.locationWhenInUse, Permission.locationAlways}),
    );
    expect(_childFinder, findsOneWidget);

    // grant the location always permission
    permissionDialogSimulator!.closePermissionHandlerDialog(
      PermissionStatus.granted,
    );
    await tester.pumpAndSettle();

    /// now we should be showing bluetooth
    expect(permissionDialogSimulator!.bluetoothDialogOpen, isTrue);
    expect(permissionDialogSimulator!.permissionHandlerDialogOpen, isFalse);
    expect(
      permissionHandlerPlatform.permissionsRequested,
      equals({Permission.locationWhenInUse, Permission.locationAlways}),
    );
    expect(_childFinder, findsOneWidget);

    // grant bluetooth
    permissionDialogSimulator!.closeBluetoothDialog(
      BluetoothPermissionStatus.granted,
    );
    await tester.pumpAndSettle();

    // now we should see the child widget
    expect(_childFinder, findsOneWidget);
  });
}

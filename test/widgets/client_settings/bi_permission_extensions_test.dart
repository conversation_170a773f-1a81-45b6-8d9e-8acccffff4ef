import 'package:bi_permissions/bi_permissions.dart';
import 'package:embark/client_settings/cubit/bi_permission_extensions.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../unit_test.mocks.dart';

void main() {
  late BiSupportedPermissions permissions;
  late Set<BiPermission> blockingUiPermissions;

  setUp(() {
    permissions = BiSupportedPermissions(bluetoothApi: MockBiBluetoothApi());
    blockingUiPermissions = {
      permissions.bluetooth,
    };
  });

  test('canBlockUiOnFirstDenial is correct for all permissions', () {
    for (final permission in permissions.all) {
      final shouldBlock = blockingUiPermissions.contains(permission);
      expect(permission.canBlockUiOnFirstDenial, equals(shouldBlock));
    }
  });
}

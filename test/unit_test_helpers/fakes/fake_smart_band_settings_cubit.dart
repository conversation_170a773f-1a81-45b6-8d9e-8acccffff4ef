import 'dart:async';

import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:embark/settings/cubit/smart_band_settings_cubit.dart';
import 'package:embark/settings/cubit/smart_band_settings_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FakeSmartBandSettingsCubit extends Cubit<SmartBandSettingsState>
    with SubscriptionWatcher
    implements SmartBandSettingsCubit {
  FakeSmartBandSettingsCubit(super.initialState);
  FakeSmartBandSettingsCubit.disabled()
      : super(
          const SmartBandSettingsState(
            connectedToDevice: false,
            status: SmartBandSettingsStatus.notAvailable,
          ),
        );
  @override
  Future<void> checkForUpdate() => checkForUpdateFuture;
  Future<void> checkForUpdateFuture = () async {}();
  @override
  Future<void> initialize() async {}

  @override
  void setSpecificFirmwareVersionToDownload(String? version) {}
}

import 'dart:async';
import 'package:bi_permissions/permissions/_bi_permission.dart';

class FakeBiPermission extends BiPermission {
  final FutureOr<void> Function(FakeBiPermission self)? onRequest;
  final FutureOr<bool> Function()? isGranted;
  final FutureOr<bool> Function()? onShouldShowRationale;
  final FutureOr<bool> Function()? isPerminentlyDenied;

  @override
  final BiPermissionType type;

  const FakeBiPermission({
    required this.type,
    this.onRequest,
    this.isGranted,
    this.onShouldShowRationale,
    this.isPerminentlyDenied,
  });

  @override
  Future<bool> get shouldShowRationale async =>
      await onShouldShowRationale?.call() ?? false;

  @override
  Future<bool> get granted async => await isGranted?.call() ?? false;

  @override
  Future<void> request() async {
    await onRequest?.call(this);
  }

  @override
  Future<bool> get permanentlyDenied async =>
      await isPerminentlyDenied?.call() ?? false;
}

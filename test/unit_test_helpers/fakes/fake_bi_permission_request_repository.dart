import 'dart:async';

import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';

class FakeBiPermissionRequestRepository extends BiPermissionRequestRepository {
  final Map<BiPermissionType, bool> _responses = {};
  final Set<BiPermissionType> permissionsRequested = {};

  void setResponse(BiPermissionType type, bool response) {
    _responses[type] = response;
  }

  FakeBiPermissionRequestRepository({
    required super.bluetoothApi,
  });

  @override
  Future<bool> requestPermission(BiPermission permission) async {
    permissionsRequested.add(permission.type);
    return _responses[permission.type] ?? false;
  }
}

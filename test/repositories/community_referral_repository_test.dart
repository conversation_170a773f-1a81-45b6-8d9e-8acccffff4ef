import 'dart:convert';

import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:bi_flutter_ui_test/helpers/_test_data_loader.dart';
import 'package:embark/extensions/client/_client_response_extension.dart';
import 'package:embark/extensions/client/_community_referral_extension.dart';
import 'package:embark/repositories/_community_referral_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:widgets/widgets.dart';

import '../unit_test.mocks.dart';

void main() {
  group('CommunityReferralRepository', () {
    final testDataLoader = TestDataLoader();
    late CommunityReferralRepository repository;
    late MockSmartLinkCommunityProviderHttpClient client;
    late MockClientRepository clientRepository;
    late MockCommunityReferralBox communityReferralBox;
    late GetClientResponse? clientResponse;

    Future<GetClientResponse?> loadClientResponseFromFile() async {
      final json = jsonDecode(
        await testDataLoader.loadRelative('json/client_info.json'),
      ) as Map<String, dynamic>;
      return GetClientResponse.fromJson(json);
    }

    final communityReferral = CommunityReferral(
      id: 1,
      name: 'Test Referral Name 1',
      referralStatus: 'Pending',
    );

    setUp(() async {
      client = MockSmartLinkCommunityProviderHttpClient();
      clientRepository = MockClientRepository();
      communityReferralBox = MockCommunityReferralBox();
      repository = CommunityReferralRepository(
        client,
        clientRepository,
        communityReferralBox,
      );
      clientResponse = await loadClientResponseFromFile();
    });

    test('saveLocally saves to box', () async {
      await repository.saveLocally(
        DataOperationType.none,
        communityReferral,
      );
      verify(communityReferralBox.put(communityReferral.id, any));
    });

    test('getAllLocalCommunityReferralChanges returns changes', () async {
      when(communityReferralBox.values).thenReturn([communityReferral.toHiveModel()]);
      final changes = repository.getAllLocalCommunityReferralChanges();
      expect(changes, isNotEmpty);
    });

    test('getAllLocalCommunityReferralChanges returns empty list on api error', () async {
      when(communityReferralBox.values).thenThrow(ApiResponse.fromCode(httpCode: 500));
      final changes = repository.getAllLocalCommunityReferralChanges();
      expect(changes, isEmpty);
    });

    test('getAllLocalCommunityReferralChanges returns empty list on error', () async {
      when(communityReferralBox.values).thenThrow(Exception());
      final changes = repository.getAllLocalCommunityReferralChanges();
      expect(changes, isEmpty);
    });

    test('deleteLocalCopy deletes all records', () async {
      await repository.deleteLocalCopy();
      verify(communityReferralBox.clear());
    });

    test('fetchCommunityReferrals returns local changes first', () async {
      when(communityReferralBox.values).thenReturn([communityReferral.toHiveModel()]);
      final changes = await repository.fetchCommunityReferrals();
      expect(changes, isNotEmpty);
      verifyNever(clientRepository.getClient(any));
    });

    test('fetchCommunityReferrals returns client changes if no local changes', () async {
      when(communityReferralBox.values).thenReturn([]);
      when(clientRepository.getClient(any)).thenAnswer(
        (_) async => Future.value(clientResponse?.toHiveModel()),
      );
      final changes = await repository.fetchCommunityReferrals();
      expect(changes, isNotEmpty);
      verify(clientRepository.getClient(any));
    });

    test('fetchCommunityReferrals returns client changes with no community referrals', () async {
      final clientResponseNoModules = GetClientResponse(
        firstName: 'Apple',
        lastName: 'Inc',
      );
      when(communityReferralBox.values).thenReturn([]);
      when(clientRepository.getClient(any)).thenAnswer(
            (_) async => Future.value(clientResponseNoModules.toHiveModel()),
      );
      final changes = await repository.fetchCommunityReferrals();
      expect(changes, isEmpty);
      verify(clientRepository.getClient(any));
    });

    test('fetchCommunityReferrals returns empty list on api error', () async {
      when(communityReferralBox.values).thenReturn([]);
      when(clientRepository.getClient(any)).thenThrow(ApiResponse.fromCode(httpCode: 500));
      final changes = await repository.fetchCommunityReferrals();
      expect(changes, isEmpty);
      verify(clientRepository.getClient(any));
    });

    test('fetchCommunityReferrals returns empty list on error', () async {
      when(communityReferralBox.values).thenThrow(Exception());
      final changes = await repository.fetchCommunityReferrals();
      expect(changes, isEmpty);
    });

    test('fetchCommunityProviders makes API call', () async {
      final communityProviderResponse = GetCommunityProvidersResponse(communityProviders: [CommunityProvider(id: 1, name: 'Test Provider')]);
      when(client.getCommunityProviders()).thenAnswer((_) async => communityProviderResponse);

      final response = await repository.fetchCommunityProviders();

      expect(response, communityProviderResponse);
    });

    test('fetchCommunityProviders returns empty list on error', () async {
      when(client.getCommunityProviders()).thenThrow(Exception());
      final response = await repository.fetchCommunityProviders();
      expect(response.communityProviders, isEmpty);
    });

    test('fetchReferralStatus makes API call', () async {
      final referralStatusResponse = GetReferralStatusResponse(items: [IdWithName(id: 1, name: 'Test Status')]);
      when(client.getReferralStatus()).thenAnswer((_) async => referralStatusResponse);

      final response = await repository.fetchReferralStatuses();

      expect(response, referralStatusResponse);
    });

    test('fetchReferralStatus returns empty list on error', () async {
      when(client.getReferralStatus()).thenThrow(Exception());
      final response = await repository.fetchReferralStatuses();
      expect(response.items, isEmpty);
    });

    test('putReferralStatus makes API call', () async {
      final request = SaveReferralStatusRequest(id: 1, statusId: 1);
      when(client.putReferralStatus(request)).thenAnswer((_) async => ApiResponse.ok());

      final response = await repository.putReferralStatus(request);

      verify(client.putReferralStatus(request));
      expect(response, ApiResponse.ok());
    });

    test('putReferralStatus returns error on error', () async {
      when(client.putReferralStatus(any)).thenThrow(Exception('Error'));
      expect(() => repository.putReferralStatus(SaveReferralStatusRequest(id: 1, statusId: 1)), throwsException);
    });
  });
}
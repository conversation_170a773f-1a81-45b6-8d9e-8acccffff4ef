{"agencyInformation": {"address": {"address1": "6265 Gunbarrel Avenue Suite B", "city": "Boulder", "postalCode": "80301", "state": "CO"}, "phone": "*************"}, "firstName": "<PERSON>", "lastName": "Hauck", "modules": [{"address": {"addressId": 2464260, "addressTypeId": 1, "country": "United States", "latitude": 36.0297485, "longitude": -79.8423448, "timeZone": "(UTC-05:00) Eastern Time (US & Canada)", "address1": "2906 Azalea Drive", "address2": "line 2", "address3": "line 3 really long", "city": "Greensboro", "countryCode": 840, "county": "Guilford", "postalCode": "27407", "state": "NC", "timeZoneId": 1}, "id": 1, "title": "Address"}, {"contactInfo": {"contactInfoId": 2618957, "primaryPhoneLabel": "Home", "email1": "<EMAIL>", "mobilePhone1": "3039158441", "mobilePhone1Code": 1, "phone1": "3365551212", "phone1Code": 1, "primaryPhone": 1, "workPhone1": "3365551234", "workPhone1Code": 1}, "id": 2, "title": "Contact Information"}, {"transportation": [{"type": "Personal Vehicle", "color": "Blue", "id": 340968, "isPrimary": true, "make": "Chevrolet", "model": "K1500", "modelYear": "1996", "state": "NC", "typeId": 1}, {"type": "Personal Vehicle", "color": "Red", "id": 340967, "isPrimary": false, "make": "Honda", "model": "K1500CRF 250 L", "modelYear": "2016", "state": "NC", "typeId": 1}], "id": 5, "title": "Transportation Methods"}, {"personalContacts": [{"firstName": "<PERSON>", "lastName": "<PERSON>", "relationship": "Other"}, {"firstName": "<PERSON>", "lastName": "Rubble", "relationship": "Other"}, {"firstName": "<PERSON><PERSON>", "lastName": "<PERSON>", "relationship": "Other"}], "id": 6, "title": "Personal Contacts"}, {"employers": [{"address": {"addressId": 2469255, "addressTypeId": 1, "country": "United States", "countryCode": 840}, "id": 95027, "name": "Wal-Mart", "phone": "**********", "phoneCode": 1}, {"address": {"addressId": 2469256, "addressTypeId": 1, "country": "United States", "countryCode": 840}, "id": 95028, "name": "Food Lion"}], "id": 7, "title": "Employers"}, {"communityReferrals": [{"dateAssigned": "2023-09-27T00:00:00", "id": 64447, "name": "Test provider", "referralStatus": "Pending"}, {"dateAssigned": "2023-10-02T00:00:00", "id": 64519, "name": "50 Character test provider name xxxxxxxxxxxxxxxEND", "referralStatus": "Pending"}], "id": 8, "title": "Community Referrals"}]}
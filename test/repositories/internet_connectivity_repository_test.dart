import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:embark/repositories/_internet_connectivity_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  group('InternetConnectivityRepository', () {
    late InternetConnectivityRepository internetConnectivityRepository;
    late MockConnectivity connectivity;
    late StreamController<List<ConnectivityResult>> connectivityStreamController;

    setUp(() {
      connectivityStreamController = StreamController.broadcast();
      connectivity = MockConnectivity();

      when(connectivity.onConnectivityChanged)
          .thenAnswer((_) => connectivityStreamController.stream);

      internetConnectivityRepository =
          InternetConnectivityRepository(connectivity);
    });

    tearDown(() async {
      await connectivityStreamController.close();
      await internetConnectivityRepository.close();
    });

    test('updates status', () async {
      expect(internetConnectivityRepository.status, isNull);

      connectivityStreamController.sink.add([ConnectivityResult.wifi]);
      await Future<void>.delayed(Duration.zero);

      expect(internetConnectivityRepository.status, [ConnectivityResult.wifi]);
    });

    test('reachability', () async {
      when(connectivity.checkConnectivity())
          .thenAnswer((_) => Future.value([ConnectivityResult.bluetooth]));
      final reachability = await internetConnectivityRepository.reachability;
      expect(reachability, [ConnectivityResult.bluetooth]);
      await Future<void>.delayed(Duration.zero);
      expect(await internetConnectivityRepository.reachability, [ConnectivityResult.bluetooth]);
    });

    test('status', () async {
      when(connectivity.checkConnectivity())
          .thenAnswer((_) => Future.value([ConnectivityResult.bluetooth]));
      expect(internetConnectivityRepository.status, isNull);

      await internetConnectivityRepository.reachability;
      await Future<void>.delayed(Duration.zero);
      expect(internetConnectivityRepository.status, [ConnectivityResult.bluetooth]);
    });

    test('close', () async {
      expect(internetConnectivityRepository.isClosed, false);
      await internetConnectivityRepository.close();
      expect(internetConnectivityRepository.isClosed, true);
    });

    test('listening to stream seeds initial status as null if not set', () async {
      bool statusUpdated = false;
      internetConnectivityRepository.connectionStream.listen((status) {
        statusUpdated = true;
      });
      await Future<void>.delayed(Duration.zero);
      expect(internetConnectivityRepository.status, isNull);
      expect(statusUpdated, false);
    });

    test('listening to stream seeds initial status if set', () async {
      connectivityStreamController.sink.add([ConnectivityResult.wifi]);
      await Future<void>.delayed(Duration.zero);
      bool statusUpdated = false;
      internetConnectivityRepository.connectionStream.listen((status) {
        statusUpdated = true;
      });
      await Future<void>.delayed(Duration.zero);
      expect(internetConnectivityRepository.status, [ConnectivityResult.wifi]);
      expect(statusUpdated, true);
    });

    test('updating status notifies listeners', () async {
      internetConnectivityRepository.connectionStream.listen((status) {
        expect(status, [ConnectivityResult.wifi]);
      });
      connectivityStreamController.sink.add([ConnectivityResult.wifi]);
      await Future<void>.delayed(Duration.zero);
    });

    test('cancelling the stream closes the listeners', () async {
      await Future<void>.delayed(Duration.zero);
      bool statusUpdated = false;
      final subscription = internetConnectivityRepository.connectionStream.listen((status) {
        statusUpdated = true;
      });
      await subscription.cancel();

      // now adding a value should not update the status
      connectivityStreamController.sink.add([ConnectivityResult.wifi]);
      await Future<void>.delayed(Duration.zero);
      expect(statusUpdated, false);
    });
  });
}
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/databases/databases.dart';
import 'package:embark/repositories/_contact_info_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  group('ContactInfoRepository', () {
    late ContactInfoRepository contactInfoRepository;
    late MockSmartLinkContactInfoHttpClient contactInfoHttpClient;
    late MockContactInfoUpdateBox contactInfoBox;

    final contactInfoUpdate = ContactInfoUpdate.empty();
    final rules = ContactInfoRules(
      // populate the rules with typical values
      countryCodes: [IdWithName(id: 1, name: 'United States')],
      emailMaxLength: 50,
      emailPattern: '',
      phoneMaxLength: 10,
      phonePattern: '',
      primaryPhones: [
        IdWithName(id: 0, name: 'None'),
        IdWith<PERSON>ame(id: 1, name: 'Home'),
        IdWithName(id: 2, name: 'Work'),
        IdWithName(id: 3, name: 'Mobile'),
      ],
      requiredFields: [],
    );

    setUp(() {
      contactInfoHttpClient = MockSmartLinkContactInfoHttpClient();
      contactInfoBox = MockContactInfoUpdateBox();
      contactInfoRepository = ContactInfoRepository(contactInfoHttpClient, contactInfoBox);
    });

    test('deleteLocalCopy', () async {
      await contactInfoRepository.deleteLocalCopy();
      verify(contactInfoBox.deleteAll(any)).called(1);
    });

    test('getLocalContactInfoChanges', () async {
      when(contactInfoBox.values).thenReturn([contactInfoUpdate]);
      final result = await contactInfoRepository.getLocalContactInfoChanges();
      expect(result, contactInfoUpdate.toApiModel());
      verify(contactInfoBox.values).called(1);
    });

    test('postContactInfo', () async {
      when(contactInfoHttpClient.postContactInfo(any)).thenAnswer((realInvocation) async => ApiResponse.ok());
      final result = await contactInfoRepository.postContactInfo(contactInfoUpdate.toApiModel());
      expect(result, ApiResponse.ok());
      verify(contactInfoHttpClient.postContactInfo(any)).called(1);
    });

    test('saveLocally', () async {
      await contactInfoRepository.saveLocally(contactInfoUpdate.toApiModel());
      verify(contactInfoBox.deleteAll(any)).called(1);
      verify(contactInfoBox.put(contactInfoUpdate.contactInfoId, any)).called(1);
    });

    test('load finds local copy', () async {
      when(contactInfoBox.values).thenReturn([contactInfoUpdate]);
      final result = await contactInfoRepository.load();
      expect(result, contactInfoUpdate.toApiModel());
      verify(contactInfoBox.values).called(1);
      verifyNever(contactInfoHttpClient.getContactInfo());
    });

    test('load does not find local copy', () async {
      when(contactInfoBox.values).thenReturn([]);
      when(contactInfoHttpClient.getContactInfo()).thenAnswer((realInvocation) async => contactInfoUpdate.toApiModel());
      final result = await contactInfoRepository.load();
      expect(result, contactInfoUpdate.toApiModel());
      verify(contactInfoBox.values).called(1);
      verify(contactInfoHttpClient.getContactInfo()).called(1);
    });

    test('getRules', () async {
      when(contactInfoHttpClient.getRules()).thenAnswer((realInvocation) async => rules);
      final result = await contactInfoRepository.getRules();
      expect(result, rules);
      verify(contactInfoHttpClient.getRules()).called(1);
    });
  });
}
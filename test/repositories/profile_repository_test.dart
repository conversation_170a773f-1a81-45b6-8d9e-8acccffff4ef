import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/repositories/_profile_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  group('ProfileRepository', () {
    late ProfileRepository profileRepository;
    late MockSmartLinkClientHttpClient clientHttpClient;

    const client = GetClientResponse(
      firstName: 'funny',
      lastName: 'very',
    );

    setUp(() {
      clientHttpClient = MockSmartLinkClientHttpClient();
      profileRepository = ProfileRepository(clientHttpClient);
    });

    test('getClient', () async {
      when(clientHttpClient.getClient()).thenAnswer((_) async => client);
      final response = await profileRepository.getClient();
      expect(response, client);
    });
  });
}
import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/databases/key_value/models/_open_text_update.dart';
import 'package:embark/repositories/_open_text_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  group('OpenTextRepository', () {
    late OpenTextRepository openTextRepository;
    late MockOpenTextUpdateBox openTextUpdateBox;

    final questionToEdit = SelfReportQuestion.empty();
    final textUpdate = OpenTextUpdate(
      questionId: questionToEdit.questionId as int,
      questionText: questionToEdit.text,
      additionalComments: '',
    );

    setUp(() {
      openTextUpdateBox = MockOpenTextUpdateBox();
      openTextRepository = OpenTextRepository(openTextUpdateBox);
    });

    test('load', () async {
      when(openTextUpdateBox.get(any, defaultValue: anyNamed('defaultValue')))
          .thenAnswer((_) => textUpdate);
      final result = await openTextRepository.load(questionToEdit);
      expect(result, textUpdate.toApiModel());
    });

    test('loadAll', () async {
      when(openTextUpdateBox.values).thenAnswer((_) => [textUpdate]);
      final result = await openTextRepository.loadAll();
      expect(result, [textUpdate]);
    });

    test('saveLocally', () async {
      final apiModel = textUpdate.toApiModel();
      await openTextRepository.saveLocally(apiModel);
      verify(openTextUpdateBox.put(textUpdate.questionId, any)).called(1);
    });

    test('deleteLocalCopy', () async {
      await openTextRepository.deleteLocalCopy();
      verify(openTextUpdateBox.deleteAll(any)).called(1);
    });

    test('deleteLocalCopyForQuestion', () async {
      await openTextRepository.deleteLocalCopyForQuestion(questionToEdit.questionId!);
      verify(openTextUpdateBox.delete(textUpdate.questionId)).called(1);
    });
  });
}
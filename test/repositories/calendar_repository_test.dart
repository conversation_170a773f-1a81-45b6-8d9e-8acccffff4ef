import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/calendar/tab-page/models/calendar_type.dart';
import 'package:embark/databases/key_value/models/_calendar_preference.dart';
import 'package:embark/repositories/_calendar_repository.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../unit_test.mocks.dart';

void main() {
  group('CalendarRepository', () {
    late CalendarRepository calendarRepository;
    late MockSmartLinkCalendarHttpClient calendarHttpClient;
    late MockCalendarPreferenceService calendarPreferenceService;

    setUp(() {
      calendarHttpClient = MockSmartLinkCalendarHttpClient();
      calendarPreferenceService = MockCalendarPreferenceService();
      calendarRepository = CalendarRepository(
        calendarHttpClient,
        calendarPreferenceService,
      );
    });

    test('getCalendarPreference mostRecentPreference older than 12 hours', () {
      final veryOldDate = DateTime.now().subtract(const Duration(hours: 13));
      when(calendarPreferenceService.getCalendarPreference())
          .thenReturn(CalendarPreference(lastCalendarCheckDate: veryOldDate));

      final calendarPreference = calendarRepository.getCalendarPreference();
      expect(calendarPreference.lastCalendarCheckDate, isNot(veryOldDate));
    });

    test('getCalendarPreference mostRecentPreference newer than 12 hours', () {
      final veryOldDate = DateTime.now().subtract(const Duration(hours: 11));
      when(calendarPreferenceService.getCalendarPreference())
          .thenReturn(CalendarPreference(lastCalendarCheckDate: veryOldDate));

      final calendarPreference = calendarRepository.getCalendarPreference();
      expect(calendarPreference.lastCalendarCheckDate, veryOldDate);
    });

    test('getCalendarPreference returns existing calendar preference ', () {
      final veryOldDate = DateTime.now().subtract(const Duration(hours: 13));
      when(calendarPreferenceService.getCalendarPreference())
          .thenReturn(CalendarPreference(lastCalendarCheckDate: veryOldDate));

      final calendarPreference = calendarRepository.getCalendarPreference();
      expect(calendarPreference.lastCalendarCheckDate, isNot(veryOldDate));

      // now get it again
      final calendarPreference2 = calendarRepository.getCalendarPreference();
      expect(calendarPreference2, calendarPreference);
    });

    test('setCalendarPreference', () async {
      final calendarPreference =
          CalendarPreference(lastCalendarCheckDate: DateTime.now());
      await calendarRepository.setCalendarPreference(calendarPreference);
      verify(calendarPreferenceService.putCalendarPreference(calendarPreference)).called(1);
    });

    test('getPreferredCalendarType', () async {
      final calendarPreference = CalendarPreference(
          lastCalendarCheckDate: DateTime.now(),
          calendarType: CalendarType.day.value,
      );
      await calendarRepository.setCalendarPreference(calendarPreference);

      expect(calendarRepository.getPreferredCalendarType(), CalendarType.day);
      await calendarRepository.setPreferredCalendarType(CalendarType.week);
      expect(calendarRepository.getPreferredCalendarType(), CalendarType.week);
    });

    test('getCalendarItems fetches items from API', () async {
      final getCalendarItemsResponseInfo = GetCalendarItemsResponseInfo(
        calendarItems: [
          CalendarItemInfo(
            id: 1,
          ),
        ],
      );

      when(calendarHttpClient.getCalendarItems(any))
          .thenAnswer((_) async => getCalendarItemsResponseInfo);
      final calendarPreference =
      CalendarPreference(lastCalendarCheckDate: DateTime.now());
      await calendarRepository.setCalendarPreference(calendarPreference);

      final calendarItems = await calendarRepository.getCalendarItems();
      expect(calendarItems, getCalendarItemsResponseInfo.calendarItems);
    });

    test('getCalendarItems fetches no items from API', () async {
      final getCalendarItemsResponseInfo = GetCalendarItemsResponseInfo();

      when(calendarHttpClient.getCalendarItems(any))
          .thenAnswer((_) async => getCalendarItemsResponseInfo);
      final calendarPreference =
      CalendarPreference(lastCalendarCheckDate: DateTime.now());
      await calendarRepository.setCalendarPreference(calendarPreference);

      final calendarItems = await calendarRepository.getCalendarItems();
      expect(calendarItems, isEmpty);
    });

    test('getCalendarItems throws error', () async {
      when(calendarHttpClient.getCalendarItems(any))
          .thenThrow(ApiResponse.fromCode(httpCode: 500));
      final calendarPreference =
      CalendarPreference(lastCalendarCheckDate: DateTime.now());
      await calendarRepository.setCalendarPreference(calendarPreference);

      expect(() async => await calendarRepository.getCalendarItems(),
          throwsA(isA<ApiResponse>()),);
    });

    test('getCalendarRules fetches items from API', () async {
      final getCalendarRulesResponse = GetCalendarRulesResponse(
        rules: CalendarRuleInfo(code: 'code'),
      );

      when(calendarHttpClient.getCalendarRules(calendarItemId: 1))
          .thenAnswer((_) async => getCalendarRulesResponse);
      final calendarItems = await calendarRepository.getCalendarRules(calendarItemId: 1);
      expect(calendarItems, getCalendarRulesResponse.rules);
    });

    test('getCalendarRules fetches no rules from API', () async {
      final getCalendarRulesResponse = GetCalendarRulesResponse();

      when(calendarHttpClient.getCalendarRules(calendarItemId: 1))
          .thenAnswer((_) async => getCalendarRulesResponse);
      final calendarItems = await calendarRepository.getCalendarRules(calendarItemId: 1);
      expect(calendarItems, const CalendarRuleInfo());
    });

    test('getCalendarRules throws error', () async {
      when(calendarHttpClient.getCalendarRules(calendarItemId: 1))
          .thenThrow(ApiResponse.fromCode(httpCode: 500));
      final calendarItems = await calendarRepository.getCalendarRules(calendarItemId: 1);
      expect(calendarItems, const CalendarRuleInfo());
    });

    test('deleteSeriesCalendarItem makes API call', () async {
      when(calendarHttpClient.deleteSeriesCalendarItem(any, any))
          .thenAnswer((_) async => ApiResponse.ok());
      await calendarRepository.deleteSeriesCalendarItem(1, '2022-01-01');
      verify(calendarHttpClient.deleteSeriesCalendarItem(1, '2022-01-01')).called(1);
    });

    test('deleteSeriesCalendarItem API call throws error', () async {
      final apiResponse = ApiResponse.fromCode(httpCode: 500);
      when(calendarHttpClient.deleteSeriesCalendarItem(any, any))
          .thenThrow(apiResponse);
      final response = await calendarRepository.deleteSeriesCalendarItem(1, '2022-01-01');
      expect(response, apiResponse);
    });

    test('deleteNonSeriesCalendarItem makes API call', () async {
      when(calendarHttpClient.deleteNonSeriesCalendarItem(any))
          .thenAnswer((_) async => ApiResponse.ok());
      await calendarRepository.deleteNonSeriesCalendarItem(1);
      verify(calendarHttpClient.deleteNonSeriesCalendarItem(1)).called(1);
    });

    test('deleteNonSeriesCalendarItem API call throws error', () async {
      final apiResponse = ApiResponse.fromCode(httpCode: 500);
      when(calendarHttpClient.deleteNonSeriesCalendarItem(any))
          .thenThrow(apiResponse);
      final response = await calendarRepository.deleteNonSeriesCalendarItem(1);
      expect(response, apiResponse);
    });

    test('createCalendarRequest makes API call', () async {
      when(calendarHttpClient.postCalendarRules(any))
          .thenAnswer((_) async => ApiResponse.ok());
      final response = await calendarRepository.createCalendarRequest(CalendarRuleInfo());
      verify(calendarHttpClient.postCalendarRules(any)).called(1);
      expect(response, ApiResponse.ok());
    });

    test('createCalendarRequest API call throws error', () async {
      final apiResponse = ApiResponse.fromCode(httpCode: 500);
      when(calendarHttpClient.postCalendarRules(any))
          .thenThrow(apiResponse);
      final response = await calendarRepository.createCalendarRequest(CalendarRuleInfo());
      expect(response, apiResponse);
    });

    test('acknowledgeCalendarItem makes API call', () async {
      when(calendarHttpClient.postCalendarItemsAcknowledge(any))
          .thenAnswer((_) async => ApiResponse.ok());
      final response = await calendarRepository.acknowledgeCalendarItem(1, DateTime.now());
      verify(calendarHttpClient.postCalendarItemsAcknowledge(any)).called(1);
      expect(response, ApiResponse.ok());
    });

    test('acknowledgeCalendarItem API call throws error', () async {
      final apiResponse = ApiResponse.fromCode(httpCode: 500);
      when(calendarHttpClient.postCalendarItemsAcknowledge(any))
          .thenThrow(apiResponse);
      final response = await calendarRepository.acknowledgeCalendarItem(1, DateTime.now());
      expect(response, apiResponse);
    });

    test('createExceptionRequest makes API call', () async {
      when(calendarHttpClient.postCalendarException(any))
          .thenAnswer((_) async => ApiResponse.ok());
      final response = await calendarRepository.createExceptionRequest(date: DateTime.now(), id: 1, calendarRuleInfo: CalendarRuleInfo());
      verify(calendarHttpClient.postCalendarException(any)).called(1);
      expect(response, ApiResponse.ok());
    });

    test('createExceptionRequest API call throws error', () async {
      final apiResponse = ApiResponse.fromCode(httpCode: 500);
      when(calendarHttpClient.postCalendarException(any))
          .thenThrow(apiResponse);
      final response = await calendarRepository.createExceptionRequest(date: DateTime.now(), id: 1, calendarRuleInfo: CalendarRuleInfo());
      expect(response, apiResponse);
    });
  });
}

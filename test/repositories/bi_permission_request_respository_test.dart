import 'dart:async';

import 'package:async/async.dart';
import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../unit_test.mocks.dart';
import '../unit_test_helpers/fakes/fake_bi_permission.dart';
import '../unit_test_helpers/fakes/fake_permission_handler_platform.dart';

void main() {
  late BiPermissionRequestRepository repository;
  late _PermissionSimulator simulator;
  late StreamQueue<BiPermission> requests;
  late MockBiBluetoothApi bluetoothApi;
  late SharedPreferences sharedPreferences;
  late FakePermissionHandlerPlatform permissionHandlerPlatform;

  setUp(() {
    permissionHandlerPlatform = FakePermissionHandlerPlatform.init();
    bluetoothApi = MockBiBluetoothApi();
    sharedPreferences = MockSharedPreferences();
    repository = BiPermissionRequestRepository(
      bluetoothApi: bluetoothApi,
    );
    simulator = _PermissionSimulator(repo: repository);
    requests = StreamQueue(repository.permissionRequested);
  });

  tearDown(() {
    debugDefaultTargetPlatformOverride = null;
    repository.close();
    simulator.close();
  });

  test('creates supported permissions', () {
    final secondRepo = BiPermissionRequestRepository(
      bluetoothApi: bluetoothApi,
    );

    expect(
      repository.supportedPermissions == secondRepo.supportedPermissions,
      isFalse,
    );
  });

  test('emits permission request correctly', () async {
    await simulator.simulateRequest(type: BiPermissionType.bluetooth);
    final next = await requests.next;
    expect(next.type, equals(BiPermissionType.bluetooth));
  });

  test(
      'location always ignores location always requested before flag on Android',
      () async {
    await simulator.simulateRequest(type: BiPermissionType.locationAlways);

    verifyZeroInteractions(sharedPreferences);
  });

  test('does not request location always permission if when in use not granted',
      () async {
    bool requested = false;

    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.permanentlyDenied,
    );

    final result = await simulator.simulateRequest(
      type: BiPermissionType.locationAlways,
      onRequest: () => requested = true,
      isGranted: () => false,
    );
    expect(requested, isFalse);
    expect(result, isFalse);
  });

  test('does not request permission if permanently denied', () async {
    bool requested = false;

    final result = await simulator.simulateRequest(
      type: BiPermissionType.bluetooth,
      onRequest: () => requested = true,
      isGranted: () => false,
      isPerminentlyDenied: () => true,
    );
    expect(requested, isFalse);
    expect(result, isFalse);
  });

  test('requests permission correctly if not already granted', () async {
    bool requested = false;
    await simulator.simulateRequest(
      type: BiPermissionType.bluetooth,
      onRequest: () => requested = true,
      isGranted: () => false,
    );
    expect(requested, isTrue);
  });

  test('does not request permission if already granted', () async {
    bool requested = false;
    bool grantedChecked = false;
    await simulator.simulateRequest(
      type: BiPermissionType.bluetooth,
      onRequest: () => requested = true,
      isGranted: () => grantedChecked = true,
    );
    expect(requested, isFalse);
    expect(grantedChecked, isTrue);
  });
  test('returns correct true value', () async {
    final result = await simulator.simulateRequest(
      type: BiPermissionType.bluetooth,
      onRequest: () => true,
    );
    expect(result, isTrue);
  });
  test('returns correct false value', () async {
    final result = await simulator.simulateRequest(
      type: BiPermissionType.bluetooth,
      onRequest: () => false,
    );
    expect(result, isFalse);
  });

  test('supports queueing', () async {
    final queue = <BiPermissionType>[];
    permissionHandlerPlatform.setStatus(
      Permission.locationWhenInUse,
      PermissionStatus.granted,
    );
    final first = simulator.simulateRequest(
      type: BiPermissionType.bluetooth,
      onRequest: () {
        queue.add(BiPermissionType.bluetooth);
        return true;
      },
    );
    final second = simulator.simulateRequest(
      type: BiPermissionType.notification,
      onRequest: () {
        queue.add(BiPermissionType.notification);
        return false;
      },
    );
    final third = simulator.simulateRequest(
      type: BiPermissionType.locationAlways,
      onRequest: () {
        queue.add(BiPermissionType.locationAlways);
        return true;
      },
    );

    // await out of order. The resulting queue should be in the order in which
    // the requests were made. The results from waiting should be the results
    // from their respective requests.
    final results = await Future.wait([second, third, first]);
    expect(results, equals([false, true, true]));
    expect(
      queue,
      equals(
        [
          BiPermissionType.bluetooth,
          BiPermissionType.notification,
          BiPermissionType.locationAlways,
        ],
      ),
    );
  });
}

class _PermissionSimulator {
  final BiPermissionRequestRepository _repo;
  late final StreamSubscription<BiPermission> _requestSub;

  _PermissionSimulator({
    required BiPermissionRequestRepository repo,
  }) : _repo = repo {
    _requestSub = _repo.permissionRequested.listen((p) async {
      await p.request();
    });
  }

  Future<bool> simulateRequest({
    required BiPermissionType type,
    FutureOr<bool> Function()? onRequest,
    FutureOr<bool> Function()? isGranted,
    FutureOr<bool> Function()? isPerminentlyDenied,
  }) async {
    bool granted = false;
    final permission = FakeBiPermission(
      type: type,
      isPerminentlyDenied: () async {
        return isPerminentlyDenied?.call() ?? false;
      },
      onRequest: (self) async {
        granted = await onRequest?.call() ?? false;
        await _repo.permissionRequestComplete(self, granted);
      },
      isGranted: () => isGranted?.call() ?? granted,
    );

    return await _repo.requestPermission(permission);
  }

  void close() {
    _requestSub.cancel();
  }
}

import 'package:bi_flutter_smlk_api/bi_flutter_smlk_api.dart';
import 'package:embark/open_text_change_request/cubit/_open_text_change_request_cubit.dart';
import 'package:embark/open_text_change_request/cubit/_open_text_change_request_state.dart';
import 'package:embark/open_text_change_request/models/_open_text_request.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import '../../unit_test.mocks.dart';

void main() {
  group('OpenTextChangeRequestCubit', () {
    late MockOpenTextRepository openTextRepository;
    late OpenTextChangeRequestCubit cubit;

    setUp(() {
      openTextRepository = MockOpenTextRepository();
      cubit = OpenTextChangeRequestCubit(openTextRepository);
    });

    test('init', () async {
      final questionToDisplay = SelfReportQuestion.empty();
      final existingChanges = OpenTextRequest.empty();
      when(openTextRepository.load(questionToDisplay)).thenAnswer((_) => Future.value(existingChanges));

      await cubit.init(questionToDisplay: questionToDisplay);

      expect(cubit.state, equals(OpenTextChangeRequestState.loaded(existingChanges)));
    });

    test('error handling', () async {
      final questionToDisplay = SelfReportQuestion.empty();
      when(openTextRepository.load(questionToDisplay)).thenThrow(Exception());

      await cubit.init(questionToDisplay: questionToDisplay);

      expect(cubit.fatalErrorMessageStream, emits('Exception'));
    });
  });
}
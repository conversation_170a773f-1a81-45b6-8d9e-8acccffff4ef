import 'package:embark/personal_contact_change_request/models/_personal_contact_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PersonalContactType', () {
    test('values', () {
      expect(PersonalContactType.values.length, 17);
    });

    test('fromInt', () {
      expect(PersonalContactType.fromInt(1), PersonalContactType.other);
      expect(PersonalContactType.fromInt(2), PersonalContactType.parent);
      expect(PersonalContactType.fromInt(3), PersonalContactType.spouse);
      expect(PersonalContactType.fromInt(4), PersonalContactType.exSpouse);
      expect(PersonalContactType.fromInt(5), PersonalContactType.child);
      expect(PersonalContactType.fromInt(6), PersonalContactType.sibling);
      expect(PersonalContactType.fromInt(7), PersonalContactType.extendedFamily);
      expect(PersonalContactType.fromInt(8), PersonalContactType.friend);
      expect(PersonalContactType.fromInt(9), PersonalContactType.attorney);
      expect(PersonalContactType.fromInt(10), PersonalContactType.inLaw);
      expect(PersonalContactType.fromInt(11), PersonalContactType.grandparent);
      expect(PersonalContactType.fromInt(12), PersonalContactType.liveInFriend);
      expect(PersonalContactType.fromInt(13), PersonalContactType.unknown);
      expect(PersonalContactType.fromInt(14), PersonalContactType.childAsContact);
      expect(PersonalContactType.fromInt(27), PersonalContactType.guardian);
      expect(PersonalContactType.fromInt(28), PersonalContactType.victim);
      expect(PersonalContactType.fromInt(34), PersonalContactType.childsFather);
      expect(PersonalContactType.fromInt(0), PersonalContactType.other);
    });
  });
}
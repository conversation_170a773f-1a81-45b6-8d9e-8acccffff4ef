import UIKit
import flutter_local_notifications
import Flutter

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        
        // Needed to ensure that fluttter_local_notification alerts can be shown otherwise
        // it will do nothing. See here:
        // https://github.com/MaikuB/flutter_local_notifications/issues/2196#issuecomment-1936298299
        setupLocalNotifications()
        
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
    
    private func setupLocalNotifications() {
        FlutterLocalNotificationsPlugin.setPluginRegistrantCallback { (registry) in
            GeneratedPluginRegistrant.register(with: registry)
        }
        UNUserNotificationCenter.current().delegate = self as UNUserNotificationCenterDelegate
    }
}

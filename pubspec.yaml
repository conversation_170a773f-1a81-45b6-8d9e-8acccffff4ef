name: embark
description: An app for client communication.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none'

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 9.9.9
environment:
  sdk: ">=3.3.4 <4.0.0"
  flutter: ">=3.27.1"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  bi_embark_l10n:
    path: packages/bi_embark_l10n

  bi_flutter_login_widget:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^15.0.2

  bi_flutter_calendar_view:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^1.0.8
  
  bi_flutter_firebase_logging:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^3.0.0

  bi_flutter_smlk_api:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^15.0.0

  bi_flutter_font_awesome_pro:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^3.0.0

  bi_flutter_mdm_platform_api:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^2.0.0

  bi_flutter_bloc:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^1.0.0

  bi_flutter_html:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^1.0.0

  bi_flutter_bluetooth_platform_api:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^10.0.0

  bi_flutter_app_insights_logging:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^4.0.0

  bi_flutter_background_location:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^1.1.0

  bi_flutter_notifications:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^1.0.0

  helpers:
    path: packages/helpers
  bi_permissions:
    path: packages/bi_permissions
  
  # DO NOT UPDATE CAMERA PACKAGE FROM ^0.10.6 to 0.11.x. 
  # THAT IS, UNLESS YOU WANT QA TO MAKE YOU DO MORE WORK BECAUSE
  # IT BREAKS THINGS MASSIVELY (DE13637). 
  # US23796 covers actually updating this. When updated to 11.x, 
  # some devices fail to capture images when using a Samsung device on Android 14
  camera: ^0.11.1
  cupertino_icons: ^1.0.8
  async: any
  equatable: ^2.0.5
  flutter:
    sdk: flutter
  flutter_bloc: ^9.0.0
  flutter_chat_types: ^3.6.2
  flutter_chat_ui: ^1.6.12
  flutter_dotenv: ^5.1.0
  flutter_image_compress: ^2.3.0
  flutter_secure_storage: ^9.2.4
  flutter_svg: any # bound to widgets
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.1
  http: ^1.2.0
  intl: ^0.19.0
  json_annotation: ^4.9.0
  locale_names: ^1.1.1
  shared_preferences: ^2.2.3
  shimmer: ^3.0.0
  visibility_detector: ^0.4.0+2
  widgets:
    path: packages/widgets

  # DO NOT UPDATE THESE FIREBASE DEPENDENCIES.
  # google_mlkit_face_detection is a third-party package that hasn't been updated in about 4 months as of 9/10/2024.
  # It depends indirectly on an older version of certain cocoa pods. Updating any of these before that is updated will
  # lead to an unsolvable dependency conflict in the iOS build. Yay for relying on third party code.
  firebase_core: ^3.13.1
  firebase_crashlytics: ^4.3.6
  firebase_analytics: ^11.4.6

  # TO DO: Update this package when a new version becomes avaialble. Then try updating the firebase dependencies above.
  # Make sure you verify that the iOS version of the app can be built after doing that.
  google_mlkit_face_detection: ^0.13.1

  file: any
  provider: any
  clock: ^1.1.1

  flutter_local_notifications: ^19.2.1
  weekday_selector: # Temporary solution awaiting the release of a new version including the following PR: https://github.com/smaho-engineering/weekday_selector/pull/12
    git:
      url: https://github.com/smaho-engineering/weekday_selector.git
      ref: e9c604a8cabb04ea3a169c3379c270c59810e4b0
  get_it: ^8.0.3
  url_launcher: ^6.2.6
  maps_launcher: ^3.0.0+1
  freezed_annotation: ^2.4.1
  json_serializable: ^6.8.0
  audioplayers: ^6.0.0
  url_launcher_platform_interface: ^2.3.2
  collection: any
  animated_theme_switcher: ^2.0.10
  connectivity_plus: ^6.1.4
  permission_handler: ^12.0.0+1
  geolocator: ^13.0.4
  battery_plus: ^6.0.1
  phone_numbers_parser: ^9.0.3
  device_info_plus: ^11.3.0
  currency_text_input_formatter: ^2.2.3
  pinput: ^5.0.0
  open_filex: ^4.6.0
  twilio_programmable_video:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^1.1.3
  uuid: ^4.4.0
  twilio_voice:
    path: packages/twilio_voice
  path_provider: ^2.1.3
  camera_platform_interface: ^2.7.4
  image: ^4.1.7
  bloc_concurrency: ^0.3.0
  # Transitive via bluetooth package
  flutter_background_service: ^5.0.5
  wakelock_plus: ^1.2.8
  mutex: ^3.1.0
  workmanager: ^0.5.2

dev_dependencies:
  bloc_test: ^10.0.0
  build_runner: ^2.4.0
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  meta: ^1.15.0
  mockito: ^5.4.4
  bi_flutter_ui_test:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^6.0.0
  bi_flutter_lints:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: ^5.0.0
  freezed: ^2.4.1
  plugin_platform_interface: ^2.1.8
  golden_toolkit: ^0.15.0
  # added just for testing, but the version is clamped by our smlk api project.
  dio: any
  flutter_native_splash: ^2.4.0
  vector_graphics: any
  # added just for testing, but the version is clamped by our firebase_core dependency
  firebase_core_platform_interface: any
  
  # added just for testing, but the version is clamped by our firebase_crashlytics
  firebase_crashlytics_platform_interface: any
  flutter_launcher_icons: ^0.14.3
  # transitive via permission_handler package
  permission_handler_platform_interface: any

  # transitive via twilio programmable video
  twilio_programmable_video_platform_interface:
    hosted: https://onepub.dev/api/nlyuuhnofl
    version: any

  # Transitive from bi_flutter_bluetooth_api
  mcumgr_flutter: any

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/graphics/SmartLinkIcon.png"
  min_sdk_android: 21
  remove_alpha_ios: true



# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/graphics/
    - assets/graphics/smartband/
    - .env
    - assets/sounds/
    - assets/test/
    
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

{"languageSelection": "Sélection de la langue", "selectALanguageToBegin": "Sélectionnez une langue pour commencer", "continueCommon": "<PERSON><PERSON><PERSON>", "arabic": "arabe", "somali": "somali", "urdu": "ourdou", "bengali": "bengali", "chineseSimplified": "chinois (simplifié)", "french": "Français", "haitianCreole": "c<PERSON><PERSON>", "hindi": "hindi", "portuguese": "portugais", "punjabi": "<PERSON><PERSON><PERSON><PERSON>", "romanian": "<PERSON><PERSON><PERSON><PERSON>", "russian": "russe", "spanish": "Espagnol", "turkish": "turc", "ukrainian": "ukrainien", "vietnamese": "vietnamien", "termsAndConditions": "Te<PERSON><PERSON> et conditions", "back": "<PERSON><PERSON>", "acknowledge": "Reconnaître", "acknowledgeViewed": "Remerciements réussis", "acknowledgeViewedFailed": "Échec de l'accusé de réception", "denverOmeletHistory": "L'histoire américaine a également son mot à dire sur la première préparation d'une omelette. Le sandwich Denver, composé d'une omelette prise en sandwich entre deux tranches de pain grillé, était une version antérieure de l'omelette Denver. Ce sandwich est devenu populaire vers 1900, et c'est plus tard que sa version omelette a été découverte. Selon la tradition, les œufs apportés par un chariot avaient un goût de renfermé et l'omelette Denver était préparée pour atténuer ce goût désagréable. La recette de l'omelette Denver se compose d'oignons, de fromage, de jambon coupé en dés, de poivron vert, de champignons et, bien sûr, d'œufs.", "enterUsernamePassword": "Entrez votre nom d'utilisateur et votre mot de passe", "username": "Nom d'utilisateur", "password": "Mot de passe", "login": "Se connecter", "resetPassword": "Réinitialiser le mot de passe", "resetPasswordPopUpText": "Êtes-vous sûr de vouloir réinitialiser le mot de passe ?", "yes": "O<PERSON>", "no": "Non", "helloWelcome": "Bonjour, bienvenue !", "toDoTasks": "{count, plural, =0{Tâ<PERSON> à effectuer} other{Tâches à effectuer ( {count} )}}", "completed": "Complété", "home": "<PERSON><PERSON>", "selfReport": "Auto-évaluation", "notificationHistory": "Historique des notifications", "settings": "Paramètres", "logOut": "Se déconnecter", "cancel": "Annuler", "send": "Envoyer", "addComment": "Ajouter un commentaire", "youHaveNothingToComplete": "Vous n'avez rien à compléter", "joinVideoConference": "Rejoignez la vidéoconférence", "submitDocumentType": "Soumettre {documentType}", "newMessage": "Nouveau message", "view": "Voir", "now": "Maintenant", "day": "Jour", "days": "Jours", "helpLayer": "Nous avons traduit cette conversation de l'anglais vers l'espagnol. Vous pouvez désactiver la traduction à tout moment ou modifier votre langue préférée dans les paramètres.", "messages": "Messages", "read": "<PERSON><PERSON>", "delivered": "Livré", "fullWeekdayDate": "{date}", "documentTypeTitle": "Type de document", "documentTypeName": "{documentType}", "cannotOpenExternalApp": "Aucune application ne peut effectuer cette action.", "addPage": "Ajouter une page", "addDocument": "Ajouter un document", "myDocuments": "Mes documents", "noDocuments": "Aucun document trouvé", "cancelDocumentSubmissions": "Voulez-vous annuler la soumission ?", "documentCaptureInstructions": "Prenez une photo du document que vous souhaitez envoyer. Si votre document comporte plusieurs pages, prenez une photo par page, jusqu'à {maximum_count} pages.", "type": "Taper", "appointmentName": "Nom du rendez-vous", "appointmentComments": "Commentaires sur le rendez-vous", "appointmentRecurrenceType": "Type de récurrence de rendez-vous", "recurrence": "<PERSON><PERSON><PERSON><PERSON>", "recurrenceDaysOfWeek": "Jours de récurrence de la semaine", "recurrsEvery": "Se reproduit toutes les", "recurrenceDaysOfWeekErrorText": "<PERSON><PERSON> de<PERSON> sélectionner au moins un jour de la semaine", "dateAndTime": "Date et heure", "address": "<PERSON><PERSON><PERSON>", "phoneNumber": "Numéro de téléphone", "getDirections": "Obtenir l'itinéraire", "call": "<PERSON><PERSON>", "noWebsiteAvailable": "Aucun site Web disponible", "requestScheduleChange": "De<PERSON><PERSON> de changement d'horaire", "requestScheduleDelete": "Demande de suppression de planification", "participants": "Participants", "authenticationFailure": "Échec d'authentification", "invalidUsername": "nom d'utilisateur invalide", "invalidPassword": "mot de passe invalide", "omelet": "<PERSON><PERSON><PERSON>", "forgotPassword": "Mot de passe oublié", "logIn": "Se connecter", "selectLanguage": "Sélectionner la langue", "language": "<PERSON><PERSON>", "passwordResetSent": "Un e-mail a été envoyé au nom d'utilisateur fourni avec des instructions pour réinitialiser votre mot de passe.", "xNumberOfDays": "{x} jours", "oneDay": "1 jour", "oneHour": "1 heure", "xNumberOfHours": "{x} heures", "today": "<PERSON><PERSON><PERSON>'hui", "tomorrow": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noItemsToDisplay": "Aucun élément à afficher", "week": "<PERSON><PERSON><PERSON>", "weeks": "Se<PERSON>ines", "month": "<PERSON><PERSON>", "list": "Liste", "to": "à", "dateRange": "{startDate} à {endDate}", "date": "{date}", "dateMonthYear": "{date}", "dateWeekday": "{date}", "dateHourOfDay": "{date}", "standInTitle": "Titre de remplacement", "appointmentChange": "Changement de rendez-vous", "requestAppointment": "Demander un rendez-vous", "calendar": "<PERSON><PERSON><PERSON>", "notImplemented": "Non mis en œuvre", "requestSubmitted": "<PERSON><PERSON><PERSON> soumise", "submit": "So<PERSON><PERSON><PERSON>", "requestToDelete": "<PERSON><PERSON><PERSON> de <PERSON>", "submitRequestFailed": "La soumission de la demande a échoué", "noMessages": "Vous n'avez aucun message", "translationMessage": "Nous avons traduit cette conversation. Vous pouvez dés<PERSON>r la traduction à tout moment ou modifier votre langue préférée dans les paramètres.", "dontAskAgain": "Ne demandez plus", "okay": "D'accord", "ok": "D'accord", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Fin des temps", "startDate": "Date de début", "endDate": "Date de fin", "biometrics": "Biométrie", "privacyPolicy": "politique de confidentialité", "terms": "Te<PERSON><PERSON> et conditions", "submitFeedback": "Soumettre des commentaires", "profile": "Profil", "preferredLanguage": "Langue préférée", "mondayShort": "L", "tuesdayShort": "M", "wednesdayShort": "M", "thursdayShort": "J", "fridayShort": "V", "saturdayShort": "S", "sundayShort": "D", "oneTime": "Une fois", "daily": "Tous les jours", "weekly": "Hebdomadaire", "debugTools": "OUTILS DE DÉBOGAGE", "privacyPolicyTitle": "politique de confidentialité", "appointmentInPastValidation": "Un rendez-vous ne peut pas être demandé dans le passé", "fieldRequired": "Requis", "maxDate": "Date maximale", "pendingTasks": "{count, plural, =0{Tâches en attente} other{Tâches en attente ( {count} )}}", "xMinutesAgo": "{count, plural, =0{Maintenant} =1{il y a 1 minute} other{il y a {count} minutes}}", "xMinutesInFuture": "{count, plural, =0{Maintenant} =1{Dans 1 minute} other{Dans {count} minutes}}", "xHoursAgo": "{count, plural, =0{Maintenant} =1{il y a 1 heure} other{il y {count} heures}}", "xHoursInFuture": "{count, plural, =0{Maintenant} =1{Dans 1 heure} other{Dans {count} heures}}", "xDaysAgo": "{count, plural, =0{Maintenant} =1{il y a 1 jour} other{il y a {count} jours}}", "xDaysInFuture": "{count, plural, =0{<PERSON><PERSON><PERSON>'hui} =1{<PERSON><PERSON><PERSON>} other{Dans {count} jours}}", "join": "Rejoindre", "reply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contactInformationLabel": "Coordonnées", "agencyPhoneNumberLabel": "Numéro de téléphone de l'agence", "agencyAddressLabel": "Adresse de l'agence", "residenceInformationLabel": "Informations sur la résidence", "reload": "Recharger", "primary": "Primaire", "noTransportation": "Pas de transport", "addTransportation": "Ajouter un transport", "filter": "Filtre", "all": "Tous", "transportation": "Transport", "unknown": "Inconnu", "genericError": "Une erreur s'est produite", "comments": "Commentaires", "state": "État", "color": "<PERSON><PERSON><PERSON>", "make": "Faire", "model": "<PERSON><PERSON><PERSON><PERSON>", "modelYear": "<PERSON><PERSON> modèle", "plate": "Plaque", "errorModelYear": "Veuillez saisir une année valide à 4 chiffres.", "clientOwned": "Propriété du client", "primaryTransportation": "Transport primaire", "tooLong": "Trop long", "appointmentDetailsTitle": "<PERSON><PERSON><PERSON> du rendez-vous", "website": "Site web", "viewWebsite": "Voir le site Web", "appointmentDateAndTime": "Date et heure du rendez-vous", "websiteGeneralError": "Erreur lors de l'accès au site Web", "phoneGeneralError": "<PERSON><PERSON><PERSON> lors de l'appel", "deleteTransporationConfirm": "Êtes-vous sûr de vouloir supprimer votre moyen de transport ?", "addressN": "<PERSON>ress<PERSON> {n}", "timeZone": "<PERSON><PERSON> ho<PERSON>", "city": "Ville", "invalidInputFormat": "Format invalide", "noEndDate": "Pas de date de fin", "appointmentRecurrsEvery": "Le rendez-vous revient tous les", "deleteEmploymentConfirm": "Êtes-vous sûr de vouloir supprimer votre emploi ?", "employment": "Emploi", "noEmployment": "Pas d'emploi", "addEmployment": "Ajouter un emploi", "employer": "Employeur", "fax": "Fax", "jobTitle": "Titre d'emploi", "supervisor": "Superviseur", "hoursPerWeek": "Heures par semaine", "schedule": "<PERSON><PERSON><PERSON>", "initialHourlyPay": "Salaire horaire initial", "finalHourlyPay": "Salaire horaire final", "emailLabel": "E-mail", "primaryPhoneLabel": "Numéro de téléphone principal", "homePhoneLabel": "Numéro de domicile", "mobilePhoneLabel": "Numéro de portable", "workPhoneLabel": "Numéro de travail", "workPhoneLabelSelection": "Travail", "mobilePhoneSelectionLabel": "Mobile", "primaryPhoneSelectionBlank": "Le numéro de téléphone principal ne peut pas être vide", "zipCode": "Code postal", "county": "Comté", "personalVehicle": "Véhicule personnel", "publicTransportation": "Transports en commun", "personalContact": "Contact personnel", "taxi": "Taxi", "bicycle": "<PERSON><PERSON><PERSON>", "walk": "Marcher", "otherSpecify": "<PERSON><PERSON> (préciser)", "notes": "<PERSON><PERSON><PERSON>", "personalContacts": "Contacts personnels", "additional": "Supplémentaire", "response": "Réponse", "selfReportSubmitted": "Auto-rapport soumis !", "selfReportError": "Erreur lors de l'envoi de l'auto-rapport !", "serverError": "Une erreur interne s'est produite lors du traitement de votre demande. Veuillez contacter votre responsable hiérarchique.", "firstName": "Prénom", "lastName": "Nom de famille", "middleInitial": "Initiale du milieu", "citizenship": "Citoyenneté", "dateOfBirth": "Date de naissance", "other": "<PERSON><PERSON>", "parent": "<PERSON><PERSON>", "spouse": "Conjoint", "exSpouse": "Ex-conjoint", "child": "<PERSON><PERSON>", "sibling": "Frère et sœur", "grandparent": "Grand-parent", "extendedFamily": "Famille élargie", "friend": "<PERSON><PERSON>", "attorney": "Avocat", "inLaw": "En droit", "guardian": "<PERSON><PERSON><PERSON>", "liveInFriend": "Vivre en ami", "victim": "Vic<PERSON>", "childsFather": "Père de l'enfant", "childAsContact": "L'enfant comme contact", "description": "Description", "livesWithClient": "Vit avec le client", "isProBono": "Bénévolat", "dependentOfClient": "Dépendant du client", "noPersonalContact": "Aucun contact personnel", "addPersonalContact": "Ajouter un contact personnel", "deletePeronalContactConfirm": "Êtes-vous sûr de vouloir supprimer votre contact personnel ?", "deleteAppointmentConfirm": "Êtes-vous sûr de vouloir supprimer votre rendez-vous ?", "failedToLoadCalendar": "Échec du chargement des éléments du calendrier. Veuillez réessayer ou contacter votre responsable.", "invalidDate": "Date invalide", "communityReferral": "Orientation communautaire", "status": "Statut", "appointmentTime": "<PERSON>ure du rendez-vous", "appointmentRecurrence": "<PERSON><PERSON><PERSON><PERSON> de rendez-vous", "appointmentNoChangesMade": "Aucune modification n'a été apportée aux informations. Veuillez les modifier pour les soumettre.", "appointmentNoChangesMadeTitle": "Aucun changement", "noInternetConnection": "Pas de connexion Internet", "configurationUpdated": "Configuration mise à jour", "pending": "En attente", "ongoing": "En cours", "utilized": "<PERSON><PERSON><PERSON><PERSON>", "notUtilized": "Non utilisé", "instructions": "Instructions", "contactNotes": "Notes de contact", "none": "Aucun", "resources": "Ressources", "documents": "Documents", "documentsSubmited": "Document soumis !", "beginSetup": "Commencer la configuration", "mdmAppPermissionNotGranted": "L'application MDM Plugin ne dispose pas de l'autorisation Téléphone. Veuillez vérifier les autorisations de l'application MDM Plugin ou la configuration Samsung Knox.", "phoneNotAssigned": "Téléphone non attribué", "missingMdmPhoneNumber": "Impossible de déterminer le numéro de téléphone. Veuillez vous assurer qu'une carte SIM est installée et réessayer.", "hours": "<PERSON><PERSON>", "name": "Nom", "resource": "Ressource", "businessHours": "Heures d'ouverture", "badLink": "Le lien fourni ne peut pas être résolu", "endMeeting": "FIN DE LA RÉUNION", "videoLoadingMessage": "Connexion à la salle...", "emptyVideoRoomMessage": "En attente qu'un autre participant se connecte à la salle...", "nearByDevices": "Appareils à proximité", "camera": "Caméra", "microphone": "Microphone", "location": "Emplacement", "permissionDialogTitle": "Autorisations nécessaires pour continuer ", "noAvailableVCErrorMessage": "Aucun appel disponible pour le moment", "sessionExpired": "La session a expiré", "accept": "Accepter", "retake": "Reprendre", "usePhoto": "Utiliser une photo", "upload": "{count, plural, other{Télécharger ( {count} )}}", "vcDialogTitle": "<PERSON><PERSON> vidéoconférence entrant", "enrollment": "Inscription", "guidelines": "Lignes directrices", "fullFrameFace": "Visage entièrement encadré", "simpleBackground": "Arrière-plan simple", "neutralExpression": "Expression neutre", "levelCamera": "Caméra alignée à niveau", "optimalDistance": "Distance optimale", "goodLighting": "<PERSON>", "doNot": "Ne pas ", "noSmile": "sourire, tourner la tête ou fermer les yeux", "noOtherPeople": "inclure plusieurs personnes ou animaux", "extendArm": "redressez ou étendez votre bras", "noFunnyFaces": "utiliser des lèvres de canard ou des angles étranges", "wearSunglasses": "porter des lunettes de soleil", "noCamerasAvaliable": "Il semble qu'aucune caméra ne soit disponible. Veuillez fermer l'application et réessayer.", "callHistory": "Historique des appels", "callHistoryLoadingMessage": "Chargement de votre historique d'appels...", "settingPin": "Configurez votre code PIN", "enterYourPin": "Entrez votre code PIN", "reEnterYourPin": "Confirmez votre code PIN", "pinsDoNotMatch": "Code PIN non concordant. Réessayez.", "pinSetupComplete": "Configuration du code PIN terminée", "incorrectPin": "Code PIN incorrect", "resetPin": "Réinitialiser le code PIN", "pinResetQuestion": "Souhaitez-vous réinitialiser votre code PIN ?", "tryingToResetPin": "Tentative de réinitialisation du code PIN", "pinResetSuccess": "Réinitialisation du code PIN réussie", "pinResetRequestFailed": "Échec de la réinitialisation du code PIN. Veuillez réessayer.", "forgotPin": "Code PIN oublié", "waitingForVerification": "En attente de vérification", "waitingForVerificationTrailing": "En attente de vérification...", "enrollmentComplete": "Inscription soumise !", "submittingEnrollment": "Soumission de l'inscription", "enrollmentError": "Une erreur s'est produite lors de l'inscription. Veuillez consulter les instructions et réessayer.", "enrollmentDescription": "L'inscription nécessite une série de photos de vous. Cliquez sur « Continuer » pour ouvrir la vue de l'appareil photo. Centrez votre visage dans le contour et attendez que l'appareil photo prenne votre photo.", "callHistoryBannerError": "Échec du chargement de votre historique d'appels. Veuillez réessayer ou contacter votre superviseur.", "checkIn": "Enregistrement", "uploadLogs": "Télécharger les journaux", "phoneCallDialogTitle": "A<PERSON> entrant", "callInProgress": "Appel en cours", "openPhoneAccountSettings": "Veuillez ouvrir l'enregistrement BI SmartLink dans les paramètres de votre compte téléphonique,", "dismiss": "<PERSON><PERSON><PERSON>", "report": "Rapport", "submittingCheckIn": "Soumission de l'enregistrement", "sessionExpiredMessage": "Votre session a expiré. Veuillez vous reconnecter pour continuer à utiliser l'application.", "updateRequired": "Mise à jour requise", "installUpdate": "Installer la mise à jour", "callerHungUpMessage": "L'appelant a raccroché.", "emptyCallHistoryMessage": "Aucun appel récent", "voipCallPermissionsDialogMessage": "Pour garantir une fonctionnalité complète, veuillez activer les autorisations d'appel VoIP dans vos paramètres.", "noCheckInsAllowed": "Aucun enregistrement n'est autorisé avant {futureDate} sauf si vous recevez une notification vous invitant à en effectuer un.", "biometricCheckInDue": "Enregistrement biométrique dû", "selfReportCheckInDue": "Date limite d'enregistrement de l'auto-évaluation", "newCalendarItem": "Nouvel élément de calendrier", "newDocument": "Nouveau document", "newVideoConference": "Nouvelle vidéoconférence", "calendarChangeRequestSubmitted": "Demande de changement soumise !", "requestNotificationResponse": "Vous recevrez une notification lorsque votre agent r<PERSON><PERSON>ndra.", "permissionRequired": "Autorisation requise", "permissionRequiredToUseThisPlugin": "Autorisation requise pour utiliser ce plugin.", "street": "Rue", "withColonSuffixAndSpaces": "{value} : ", "warning": "Avertissement", "enableLocation": "Les services de localisation de l'appareil sont actuellement désactivés. Veuillez les activer pour l'application. ", "streetName": "Nom de la rue", "genericChangeRequestSubmitted": "Demande de changement soumise !", "withSpaceDashShapce": "{value} - ", "prefixWithProfile": "Profil - {value}", "vcHungUpMessage": "Aucun appel disponible pour le moment.", "profileNoContacts": "Aucun contact", "relationship": "Relation", "checkInDescription": "Veuillez aligner votre visage sur le contour tout en tenant votre téléphone à hauteur des yeux. Votre photo sera prise automatiquement.", "checkInComplete": "Enregistrement effectué !", "takePhoto": "<PERSON><PERSON><PERSON> une photo", "referral": "Orientation", "restartPhone": "Veuillez redémarrer le téléphone", "gettingStarted": "Commencer", "newFeatures": "SmartLINK Nouvelle conception et nouvelles fonctionnalités", "done": "Fait", "next": "Suivant", "previous": "Précédent", "todoTasksTitle": "T<PERSON>ches à effectuer", "hamburgerMenu": "Éléments du menu Hamburger", "myInfo": "Le profil remplace Mes informations", "dashboardTabs": "Page d'accueil", "accessCheckIn": "Enregistrement d'accès", "close": "<PERSON><PERSON><PERSON>", "enrollmentCompletePNTitle": "Inscription terminée", "fixedBiometricPNTitle": "Enregistrement biométrique", "bluetoothDisabled": "Bluetooth désactivé", "enableInSettings": "<PERSON>r dans les paramètres", "locationServiceDisabled": "Services de localisation désactivés", "enableLocationTodoItemTitle": "Services de localisation désactivés", "appName": "BI SmartLINK", "trackingRunningInBackground": "BI SmartLINK surveille votre emplacement en arrière-plan pour garantir la conformité du programme.", "centerFace": "Centrez votre visage dans le contour", "blinkAndTakeBreath": "Clignez des yeux et respirez profondément", "waitUpto15Seconds": "Attendez jusqu'à 15 secondes", "autoCapture": "Votre photo sera capturée automatiquement", "takeOff": "Enlevez vos lunettes, votre chapeau ou tout ce qui couvre votre visage", "useNeutralExpression": "Utilisez une expression neutre", "wellLit": "Assurez-vous que votre visage est bien éclairé", "joiningVideoConferenceDialogTitle": "Adhésion...", "preferences": "Préférences", "legal": "Légal", "smartband": "Bracelet intelligent", "firmwareVersion": "Version du micrologiciel", "smartbandId": "SmartBand ID", "regulatoryLabels": "Étiquettes réglementaires", "rfExposure": "Exposition aux RF", "rfExposureTitle": "Débit d'absorption spécifique (DAS)", "rfExposureParagraph1": "Cet équipement est conforme aux directives internationales relatives à l'exposition aux radiofréquences. Il est conçu pour ne pas dépasser les limites d'exposition aux radiofréquences recommandées par les directives internationales. Ces directives, élaborées par un organisme scientifique indépendant (ICNIRP), prévoient une marge de sécurité importante destinée à garantir la sécurité de tous, quels que soient leur âge et leur état de santé.", "rfExposureParagraph2": "La limite DAS pour les appareils mobiles est de 1,6 W/kg avec une distance de séparation comme suit :", "rfExposureHead": "Tête – 0 mm", "rfExposureBody": "Corps – 10 mm", "designedTested": "Conçu, testé et assemblé aux États-Unis", "smartbandColor": "Couleur : Noir/Bleu", "notAssigned": "Non attribué", "appSettingsVersionText": "Version {version}", "fccId": "Contient l'ID FCC : {fccId}", "icId": "Contient l'identifiant ISED : {isedId}", "biRegulatoryModel": "MODÈLE BI : {model}", "goToSettings": "Accédez aux paramètres", "enableBluetooth": "Activez Bluetooth pour vous connecter à votre appareil SmartBand.", "bluetooth": "Bluetooth", "checkingForFotaMessage": "Vérification de la disponibilité du SmartBand FOTA...", "checkingForSpecificFotaMessage": "Vérification de la version SmartBand FOTA {version} ...", "smartBandUpdateComplete": "Mise à jour du SmartBand terminée !", "smartBandUpdateFailed": "Échec de la mise à jour du micrologiciel du SmartBand.", "smartBandUpdateInvalidFirmwareFile": "Le fichier FOTA n'est pas valide. Assurez-vous que la version existe et est correcte avant de réessayer.", "checkForSmartBandFota": "Vérifiez SmartBand FOTA", "smartBandFotaVersionToDownload": "Version SmartBand FOTA à télécharger", "notificationPermissionRationale": "Cette application vous enverra des mises à jour pour vous aider à respecter les exigences de votre programme. Veuillez autoriser l'accès dans la boîte de dialogue suivante.", "genericPermissionRationale": "Cette application nécessite une autorisation supplémentaire pour répondre aux exigences de votre programme. Veuillez l'autoriser dans la boîte de dialogue suivante.", "permissionsMessage": "BI SmartLINK nécessite certaines autorisations pour fonctionner correctement. Une connexion Internet est requise pour envoyer et recevoir des données depuis nos serveurs. La caméra est nécessaire à plusieurs fonctions. Elle sert à l'authentification pour certaines activités et permet également de télécharger des documents si nécessaire. La localisation de l'appareil est utilisée lors de certaines activités. Cela peut inclure une collecte de données en arrière-plan, selon votre programme de supervision. Si vous êtes inscrit à BI SmartLink Tracking, cette application collecte, transmet et stocke la localisation de l'appareil à intervalles réguliers. Ces informations ne sont pas partagées avec des tiers. Si vous n'êtes pas inscrit à BI SmartLink Tracking, la collecte de données en arrière-plan n'est pas utilisée. Le Bluetooth peut être utilisé selon votre programme de supervision. Le Bluetooth est utilisé uniquement lorsqu'il est couplé à notre équipement BI SmartBand. Les appareils à proximité sont utilisés lors des visioconférences afin que des périphériques externes, tels que des haut-parleurs, puissent être utilisés.", "locationAlwaysPermissionRationale": "Cette application a besoin d'accéder à votre localisation à tout moment pour respecter les exigences de votre programme. Ces données sont transmises et stockées, mais ne sont pas partagées avec des tiers. Vous serez invité à activer l'autorisation « Autoriser tout le temps »."}
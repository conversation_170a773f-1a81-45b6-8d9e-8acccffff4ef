class LocaleNativeName {
  static const notFound = 'Language not found';
  static const Map<String, String> nativeNames = {
    'en': 'English',
    'es': 'español',
    'fr': 'français',
    'hi': 'हिन्दी',
    'ht': '<PERSON><PERSON><PERSON><PERSON>',
    'pa': 'ਪੰਜਾਬੀ',
    'pt': 'português',
    'ro': 'română',
    'ru': 'Русский',
    'tr': 'Türkçe',
    'uk': 'Українська',
    'vi': 'tiếng việt',
    'zh': '中文',
    'ar': 'العربية',
    'bn': 'বাংলা',
    'so': 'Soomaali',
    'ur': 'اُردُو',
  };

  static const Set<String> rtlLanguages = {'ar', 'ur'};

  static String getNativeName(String languageCode) {
    return nativeNames.containsKey(languageCode)
        ? nativeNames[languageCode]!
        : notFound;
  }

  static bool isRtl(String languageCode) {
    return rtlLanguages.contains(languageCode);
  }
}

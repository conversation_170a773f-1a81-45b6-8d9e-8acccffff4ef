import 'package:bi_embark_l10n/l10n/haitian_creole_date_time.dart';
import 'package:bi_embark_l10n/l10n/ht_cupertino_localizations_delegate.dart';
import 'package:bi_embark_l10n/l10n/ht_material_localizations_delegate.dart';
import 'package:bi_embark_l10n/l10n/so_cupertino_localizations_delegate.dart';
import 'package:bi_embark_l10n/l10n/so_date_time.dart';
import 'package:bi_embark_l10n/l10n/so_material_localizations_delegate.dart';
import 'package:flutter/material.dart';

class CustomLocaleSetup {
  static void initializeDateTimes() {
    HaitianCreoleDateTime.initialize();
    SomaliDateTime.initialize();
  }

  static const List<LocalizationsDelegate<dynamic>> delegates = [
    HtMaterialLocalizations.delegate,
    HtCupertinoLocalizations.delegate,
    SoMaterialLocalizations.delegate,
    SoCupertinoLocalizations.delegate,
  ];
}

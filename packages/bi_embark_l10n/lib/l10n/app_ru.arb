{"languageSelection": "Выбор языка", "selectALanguageToBegin": "Выберите язык, чтобы начать", "continueCommon": "Продолжать", "arabic": "арабский", "somali": "сомалийский", "urdu": "урду", "bengali": "бенгальский", "chineseSimplified": "Китайский (упрощенный)", "french": "Французский", "haitianCreole": "гаитянский креольский", "hindi": "хинди", "portuguese": "португальский", "punjabi": "пенджа<PERSON>и", "romanian": "румынский", "russian": "Русский", "spanish": "испанский", "turkish": "турецкий", "ukrainian": "украинский", "vietnamese": "вьетнамский", "termsAndConditions": "Условия и положения", "back": "Назад", "acknowledge": "Сознавать", "acknowledgeViewed": "Подтверждение успешное", "acknowledgeViewedFailed": "Подтверждение не удалось", "denverOmeletHistory": "Что касается первого приготовления омлета, то в истории Америки тоже есть своя доля. Денверский сэндвич, который состоял из омлета, зажатого двумя тостами, был более ранней версией денверского омлета. Денверский сэндвич стал популярным около 1900 года, и только позже была обнаружена его версия в виде омлета. Согласно традиционной истории, яйца, привезенные в грузовом вагоне, имели несвежий вкус, и денверский омлет был приготовлен, чтобы подавить этот неприятный вкус. Рецепт денверского омлета состоит из лука, сыра, нарезанной кубиками ветчины, зеленого перца, грибов и, конечно же, яиц.", "enterUsernamePassword": "Введите свое имя пользователя и пароль", "username": "Имя пользователя", "password": "Пароль", "login": "Авторизоваться", "resetPassword": "Сбросить пароль", "resetPasswordPopUpText": "Вы уверены, что хотите сбросить пароль?", "yes": "Да", "no": "Нет", "helloWelcome": "Здравствуйте, добро пожаловать!", "toDoTasks": "{count, plural, =0{Выполнять задачи} other{Задачи, которые нужно сделать ( {count} )}}", "completed": "Завершенный", "home": "Дом", "selfReport": "Самоотчет", "notificationHistory": "История уведомлений", "settings": "Настройки", "logOut": "Выйти", "cancel": "Отмена", "send": "Отправлять", "addComment": "Добавить комментарий", "youHaveNothingToComplete": "Вам нечего заполнять", "joinVideoConference": "Присоединяйтесь к видеоконференции", "submitDocumentType": "Отправить {documentType}", "newMessage": "Новое сообщение", "view": "Вид", "now": "Сей<PERSON><PERSON>с", "day": "День", "days": "<PERSON><PERSON>и", "helpLayer": "Мы перевели этот разговор с английского на испанский. Вы можете отключить этот перевод в любое время или изменить предпочитаемый язык в настройках.", "messages": "Сообщения", "read": "Читать", "delivered": "Доставленный", "fullWeekdayDate": "{date}", "documentTypeTitle": "Тип документа", "documentTypeName": "{documentType}", "cannotOpenExternalApp": "Ни одно приложение не может выполнить это действие.", "addPage": "Добавить страницу", "addDocument": "Добавить документ", "myDocuments": "Мои документы", "noDocuments": "Документы не найдены", "cancelDocumentSubmissions": "Вы хотите отменить отправку?", "documentCaptureInstructions": "Сфотографируйте документ, который хотите отправить. Если в документе несколько страниц, сделайте по одной фотографии на страницу, до {maximum_count} страниц.", "type": "Тип", "appointmentName": "Имя назначения", "appointmentComments": "Комментарии о назначении", "appointmentRecurrenceType": "Тип повторения приема", "recurrence": "Рецидив", "recurrenceDaysOfWeek": "Повторяющиеся дни недели", "recurrsEvery": "Повторяется каждые", "recurrenceDaysOfWeekErrorText": "Вы должны выбрать хотя бы один день недели", "dateAndTime": "Дата и время", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phoneNumber": "Номер телефона", "getDirections": "Получить указания", "call": "Вы<PERSON><PERSON>", "noWebsiteAvailable": "Сайт недоступен", "requestScheduleChange": "Запросить изменение расписания", "requestScheduleDelete": "Запросить расписание Удалить", "participants": "Участники", "authenticationFailure": "Ошибка аутентификации", "invalidUsername": "неверное имя пользователя", "invalidPassword": "неверный пароль", "omelet": "Омлет", "forgotPassword": "Забыли пароль", "logIn": "Авторизоваться", "selectLanguage": "Выберите язык", "language": "Язык", "passwordResetSent": "На указанный вами адрес электронной почты было отправлено письмо с инструкциями по сбросу пароля.", "xNumberOfDays": "{x} д<PERSON><PERSON><PERSON>", "oneDay": "1 день", "oneHour": "1 час", "xNumberOfHours": "{x} ча<PERSON>ов", "today": "Сегодня", "tomorrow": "Завтра", "refresh": "Обновить", "noItemsToDisplay": "Нет элементов для отображения", "week": "Неделя", "weeks": "Недели", "month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "list": "Список", "to": "к", "dateRange": "{startDate} по {endDate}", "date": "{date}", "dateMonthYear": "{date}", "dateWeekday": "{date}", "dateHourOfDay": "{date}", "standInTitle": "Заявка на титул", "appointmentChange": "Изменение назначения", "requestAppointment": "Запросить назначение", "calendar": "Календарь", "notImplemented": "Не реализовано", "requestSubmitted": "Запрос отправлен", "submit": "Представлять на рассмотрение", "requestToDelete": "Запрос на удаление", "submitRequestFailed": "Отправить запрос не удалось", "noMessages": "У вас нет сообщений", "translationMessage": "Мы перевели этот разговор. Вы можете отключить этот перевод в любое время или изменить предпочитаемый язык в Настройках.", "dontAskAgain": "Не спрашивай снова", "okay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ok": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "startTime": "Время начала", "endTime": "Время окончания", "startDate": "Дата начала", "endDate": "Дата окончания", "biometrics": "Биометрия", "privacyPolicy": "политика конфиденциальности", "terms": "Условия и положения", "submitFeedback": "Отправить отзыв", "profile": "Профиль", "preferredLanguage": "Предпочтительный язык", "mondayShort": "Пн", "tuesdayShort": "Вт", "wednesdayShort": "Ср", "thursdayShort": "Чт", "fridayShort": "Пт", "saturdayShort": "Сб", "sundayShort": "Вс", "oneTime": "Один раз", "daily": "Ежедневно", "weekly": "Еженедельно", "debugTools": "ИНСТРУМЕНТЫ ОТЛАДКИ", "privacyPolicyTitle": "политика конфиденциальности", "appointmentInPastValidation": "Запись на прием не может быть сделана в прошлом.", "fieldRequired": "Необходимый", "maxDate": "Макс. дата", "pendingTasks": "{count, plural, =0{Ожидаемые задачи} other{Ожидающие задачи ( {count} )}}", "xMinutesAgo": "{count, plural, =0{Сейчас} =1{1 минуту назад} other{{count} минут назад}}", "xMinutesInFuture": "{count, plural, =0{Сейчас} =1{Через 1 минуту} other{Через {count} минут}}", "xHoursAgo": "{count, plural, =0{Сейчас} =1{1 час назад} other{{count} часов назад}}", "xHoursInFuture": "{count, plural, =0{Сейчас} =1{Через 1 час} other{Через {count} часов}}", "xDaysAgo": "{count, plural, =0{Сейчас} =1{1 день назад} other{{count} дней назад}}", "xDaysInFuture": "{count, plural, =0{Сегодня} =1{Завтра} other{Через {count} дней}}", "join": "Присоединиться", "reply": "Отвечать", "contactInformationLabel": "Контактная информация", "agencyPhoneNumberLabel": "Номер телефона агентства", "agencyAddressLabel": "Адрес агентства", "residenceInformationLabel": "Информация о резиденции", "reload": "Перезагрузить", "primary": "Начальный", "noTransportation": "Нет транспорта", "addTransportation": "Добавить транспорт", "filter": "Фильтр", "all": "Все", "transportation": "Транспорт", "unknown": "Неизвестный", "genericError": "Произошла ошибка", "comments": "Комментарии", "state": "Состояние", "color": "Цвет", "make": "Дела<PERSON>ь", "model": "Модель", "modelYear": "Модельный год", "plate": "Тарелка", "errorModelYear": "Введите действительный 4-значный год.", "clientOwned": "Принадлежит клиенту", "primaryTransportation": "Первичный транспорт", "tooLong": "Слишком долго", "appointmentDetailsTitle": "Подробности назначения", "website": "Веб-сайт", "viewWebsite": "Посмотреть веб-сайт", "appointmentDateAndTime": "Дата и время приема", "websiteGeneralError": "Ошибка при доступе к сайту", "phoneGeneralError": "Ошибка при совершении вызова", "deleteTransporationConfirm": "Вы уверены, что хотите удалить свой способ передвижения?", "addressN": "Адрес {n}", "timeZone": "Часовой пояс", "city": "Город", "invalidInputFormat": "Неверный формат", "noEndDate": "Нет даты окончания", "appointmentRecurrsEvery": "Назначение повторяется каждые", "deleteEmploymentConfirm": "Вы уверены, что хотите удалить свою работу?", "employment": "Работа", "noEmployment": "Нет работы", "addEmployment": "Добавить работу", "employer": "Работодатель", "fax": "Факс", "jobTitle": "Должность", "supervisor": "Руководитель", "hoursPerWeek": "Часов в неделю", "schedule": "Расписание", "initialHourlyPay": "Первоначальная почасовая оплата", "finalHourlyPay": "Окончательная почасовая оплата", "emailLabel": "Электронная почта", "primaryPhoneLabel": "Основной номер телефона", "homePhoneLabel": "Домашний номер", "mobilePhoneLabel": "Номер мобильного телефона", "workPhoneLabel": "Рабочий номер", "workPhoneLabelSelection": "Работа", "mobilePhoneSelectionLabel": "Мобильный", "primaryPhoneSelectionBlank": "Основной номер телефона не может быть пустым.", "zipCode": "Почтовый индекс", "county": "Графство", "personalVehicle": "Личный автомобиль", "publicTransportation": "Общественный транспорт", "personalContact": "Личный контакт", "taxi": "Такси", "bicycle": "Велосипед", "walk": "<PERSON>одить", "otherSpecify": "Другое (укажите)", "notes": "Примечания", "personalContacts": "Личные контакты", "additional": "Дополнительный", "response": "Ответ", "selfReportSubmitted": "Самоотчет отправлен!", "selfReportError": "Ошибка при отправке самоотчета!", "serverError": "При обработке вашего запроса произошла внутренняя ошибка. Обратитесь к своему руководителю.", "firstName": "Имя", "lastName": "Фамилия", "middleInitial": "Второй инициал", "citizenship": "Гражданство", "dateOfBirth": "Дата рождения", "other": "Друг<PERSON>й", "parent": "Родитель", "spouse": "Супруг", "exSpouse": "<PERSON>ы<PERSON><PERSON>ий супруг", "child": "Ребенок", "sibling": "Брат или сестра", "grandparent": "Дедушка или бабушка", "extendedFamily": "Большая семья", "friend": "Друг", "attorney": "Адвокат", "inLaw": "По закону", "guardian": "Храни<PERSON>ель", "liveInFriend": "Живите в Друге", "victim": "Жертва", "childsFather": "Отец ребенка", "childAsContact": "Ребенок как контакт", "description": "Описание", "livesWithClient": "Живет с клиентом", "isProBono": "Про Боно", "dependentOfClient": "Зависит от клиента", "noPersonalContact": "Никаких личных контактов", "addPersonalContact": "Добавить личный контакт", "deletePeronalContactConfirm": "Вы уверены, что хотите удалить свой личный контакт?", "deleteAppointmentConfirm": "Вы уверены, что хотите удалить свою встречу?", "failedToLoadCalendar": "Не удалось загрузить элементы календаря. Попробуйте еще раз или свяжитесь с вашим руководителем.", "invalidDate": "Неверная дата", "communityReferral": "Направление сообщества", "status": "Статус", "appointmentTime": "Время приема", "appointmentRecurrence": "Повторение назначений", "appointmentNoChangesMade": "Информация не была изменена. Отредактируйте информацию для отправки.", "appointmentNoChangesMadeTitle": "Никаких изменений.", "noInternetConnection": "Нет подключения к Интернету", "configurationUpdated": "Конфигурация обновлена", "pending": "В ожидании", "ongoing": "Непрерывный", "utilized": "Использованный", "notUtilized": "Не используется", "instructions": "Инструкции", "contactNotes": "Контакты Примечания", "none": "Никто", "resources": "Ресурсы", "documents": "Документы", "documentsSubmited": "Документ отправлен!", "beginSetup": "Начать настройку", "mdmAppPermissionNotGranted": "MDM Plugin App не имеет разрешения на телефон. Проверьте разрешения MDM Plugin App или конфигурацию Samsung Knox.", "phoneNotAssigned": "Телефон не назначен", "missingMdmPhoneNumber": "Номер телефона не может быть определен. Убедитесь, что SIM-карта установлена, и повторите попытку.", "hours": "<PERSON>а<PERSON>ы", "name": "Имя", "resource": "Ресурс", "businessHours": "Часы работы", "badLink": "Предоставленная ссылка не может быть разрешена", "endMeeting": "КОНЕЦ ВСТРЕЧИ", "videoLoadingMessage": "Подключение к комнате...", "emptyVideoRoomMessage": "Ожидание подключения к комнате другого участника...", "nearByDevices": "Устройства поблизости", "camera": "Камера", "microphone": "Микрофон", "location": "Расположение", "permissionDialogTitle": "Для продолжения необходимы разрешения ", "noAvailableVCErrorMessage": "В данный момент нет доступных звонков", "sessionExpired": "Время сеанса истекло", "accept": "Принимать", "retake": "Повторная сдача", "usePhoto": "Использовать фото", "upload": "{count, plural, other{Загрузить ( {count} )}}", "vcDialogTitle": "Входящий видеоконференц-звонок", "enrollment": "Зачисление", "guidelines": "Руководящие принципы", "fullFrameFace": "Полностью обрамленное лицо", "simpleBackground": "Простой фон", "neutralExpression": "Нейтральное выражение", "levelCamera": "Камера выровнена по уровню", "optimalDistance": "Оптимальное расстояние", "goodLighting": "Хорошее освещение.", "doNot": "Не ", "noSmile": "улыбнитесь, поверните голову или закройте глаза", "noOtherPeople": "включают несколько человек или животных", "extendArm": "выпрямите или вытяните руку", "noFunnyFaces": "используйте утиные губы или странные углы", "wearSunglasses": "носить солнцезащитные очки", "noCamerasAvaliable": "Похоже, что камер нет. Пожалуйста, закройте приложение и попробуйте еще раз.", "callHistory": "История звонков", "callHistoryLoadingMessage": "Загрузка истории звонков...", "settingPin": "Установите свой PIN-код", "enterYourPin": "Введите ваш PIN-код", "reEnterYourPin": "Подтвердите свой PIN-код", "pinsDoNotMatch": "Несоответствие PIN-кода. Попробуйте еще раз.", "pinSetupComplete": "Настройка PIN-кода завершена", "incorrectPin": "Неправильный PIN-код", "resetPin": "Сбросить ПИН-код", "pinResetQuestion": "Хотите сбросить свой PIN-код?", "tryingToResetPin": "Попытка сбросить PIN-код", "pinResetSuccess": "Успешно сбросить PIN-код", "pinResetRequestFailed": "Сброс PIN-кода не удался. Попробуйте еще раз.", "forgotPin": "Забыли PIN-код?", "waitingForVerification": "Ожидание проверки", "waitingForVerificationTrailing": "Ожидание проверки...", "enrollmentComplete": "Регистрация отправлена!", "submittingEnrollment": "Подача заявки на регистрацию", "enrollmentError": "Произошла ошибка при регистрации. Пожалуйста, проверьте правила и попробуйте еще раз.", "enrollmentDescription": "Для регистрации требуется серия ваших фотографий. Нажмите «Продолжить», чтобы открыть вид с камеры. Расположите свое лицо в центре контура и подождите, пока камера сделает снимок.", "callHistoryBannerError": "Не удалось загрузить историю звонков. Попробуйте еще раз или обратитесь к своему руководителю.", "checkIn": "Регистрироваться", "uploadLogs": "Загрузить журналы", "phoneCallDialogTitle": "Входящий звонок", "callInProgress": "Звонок в процессе", "openPhoneAccountSettings": "Пожалуйста, откройте регистрацию BI SmartLink в настройках учетной записи вашего телефона,", "dismiss": "Увольнять", "report": "Отчет", "submittingCheckIn": "Отправка регистрации", "sessionExpiredMessage": "Ваш сеанс истек. Пожалуйста, войдите снова, чтобы продолжить использование приложения.", "updateRequired": "Требуется обновление", "installUpdate": "Установить обновление", "callerHungUpMessage": "Звонящий повесил трубку.", "emptyCallHistoryMessage": "Нет недавних звонков", "voipCallPermissionsDialogMessage": "Для обеспечения полной функциональности включите разрешения на VoIP-вызовы в настройках.", "noCheckInsAllowed": "Отмечаться нельзя до {futureDate} если только вы не получите уведомление о необходимости это сделать.", "biometricCheckInDue": "Биометрическая регистрация обязательна", "selfReportCheckInDue": "Самостоятельная регистрация заезда", "newCalendarItem": "Новый элемент календаря", "newDocument": "Новый документ", "newVideoConference": "Новая видеоконференция", "calendarChangeRequestSubmitted": "Запрос на изменение отправлен!", "requestNotificationResponse": "Вы получите уведомление, когда ваш сотрудник ответит.", "permissionRequired": "Требуется разрешение", "permissionRequiredToUseThisPlugin": "Для использования этого плагина требуется разрешение.", "street": "Улица", "withColonSuffixAndSpaces": "{value} : ", "warning": "Предупреждение", "enableLocation": "Службы определения местоположения устройства в настоящее время отключены. Пожалуйста, включите службы определения местоположения для приложения. ", "streetName": "Название улицы", "genericChangeRequestSubmitted": "Запрос на изменение отправлен!", "withSpaceDashShapce": "{value} - ", "prefixWithProfile": "Профиль - {value}", "vcHungUpMessage": "В данный момент звонок недоступен.", "profileNoContacts": "Нет конта<PERSON>тов", "relationship": "Отношение", "checkInDescription": "Пожалуйста, расположите свое лицо в контуре, держа телефон на уровне глаз. Ваша фотография будет сделана автоматически.", "checkInComplete": "Регистрация отправлена!", "takePhoto": "Сделать фото", "referral": "Направление", "restartPhone": "Пожалуйста, перезагрузите телефон.", "gettingStarted": "Начиная", "newFeatures": "Новый дизайн и функции SmartLINK", "done": "Сделанный", "next": "Следующий", "previous": "Предыдущий", "todoTasksTitle": "Выполнять задачи", "hamburgerMenu": "Пункты меню гамбургеров", "myInfo": "Профиль заменяет Мою информацию", "dashboardTabs": "Домашняя страница", "accessCheckIn": "Доступ к регистрации", "close": "Закрывать", "enrollmentCompletePNTitle": "Регистрация завершена", "fixedBiometricPNTitle": "Биометрическая регистрация", "bluetoothDisabled": "Bluetooth отключен", "enableInSettings": "Включить в настройках", "locationServiceDisabled": "Службы определения местоположения отключены", "enableLocationTodoItemTitle": "Службы определения местоположения отключены", "appName": "BI-SmartLINK", "trackingRunningInBackground": "BI SmartLINK отслеживает ваше местоположение в фоновом режиме, чтобы обеспечить соответствие программе.", "centerFace": "Расположите лицо в центре контура.", "blinkAndTakeBreath": "Моргните и сделайте глубокий вдох.", "waitUpto15Seconds": "Подождите до 15 секунд.", "autoCapture": "Ваша фотография будет сделана автоматически.", "takeOff": "Снимите очки, шляпу и все, что закрывает ваше лицо.", "useNeutralExpression": "Используйте нейтральное выражение.", "wellLit": "Убедитесь, что ваше лицо хорошо освещено.", "joiningVideoConferenceDialogTitle": "Присоединяюсь...", "preferences": "Предпочтения", "legal": "Юридический", "smartband": "СмартБэнд", "firmwareVersion": "Версия прошивки", "smartbandId": "ID смарт-браслета", "regulatoryLabels": "Нормативные этикетки", "rfExposure": "Воздействие радиочастот", "rfExposureTitle": "Удельный коэффициент поглощения (SAR)", "rfExposureParagraph1": "Это оборудование соответствует международным рекомендациям по воздействию радиочастотной энергии. Устройство разработано таким образом, чтобы не превышать пределы воздействия радиочастотной энергии, рекомендованные международными рекомендациями. Рекомендации были разработаны независимой научной организацией (ICNIRP) и включают в себя существенный запас безопасности, призванный обеспечить безопасность всех лиц независимо от возраста и состояния здоровья.", "rfExposureParagraph2": "Предел SAR для мобильных устройств составляет 1,6 Вт/кг при следующем расстоянии между ними:", "rfExposureHead": "Головка – 0 мм", "rfExposureBody": "Корпус – 10 мм", "designedTested": "Разработано, испытано и собрано в США.", "smartbandColor": "Цвет: черный/синий", "notAssigned": "Не назначено", "appSettingsVersionText": "Версия {version}", "fccId": "Содержит идентификатор FCC: {fccId}", "icId": "Содержит идентификатор ISED: {isedId}", "biRegulatoryModel": "МОДЕЛЬ БИ: {model}", "goToSettings": "Перейти в настройки", "enableBluetooth": "Включите Bluetooth для подключения к устройству SmartBand.", "bluetooth": "Bluetooth", "checkingForFotaMessage": "Проверка наличия доступного SmartBand FOTA...", "checkingForSpecificFotaMessage": "Проверка версии SmartBand FOTA {version} ...", "smartBandUpdateComplete": "Обновление SmartBand завершено!", "smartBandUpdateFailed": "Не удалось обновить прошивку SmartBand.", "smartBandUpdateInvalidFirmwareFile": "Файл FOTA недействителен. Убедитесь, что версия существует и верна, прежде чем повторить попытку.", "checkForSmartBandFota": "Проверить наличие SmartBand FOTA", "smartBandFotaVersionToDownload": "Версия SmartBand FOTA для загрузки", "notificationPermissionRationale": "Это приложение будет отправлять вам обновления, чтобы помочь вам соответствовать требованиям вашей программы. Пожалуйста, разрешите разрешение в следующем диалоговом окне.", "genericPermissionRationale": "Этому приложению необходимо дополнительное разрешение для того, чтобы помочь вам с требованиями вашей программы. Пожалуйста, разрешите разрешение в следующем диалоговом окне.", "permissionsMessage": "Для правильной работы BI SmartLINK требуются определенные разрешения. Для отправки и получения данных с наших серверов требуется подключение к Интернету. Камера необходима для ряда функций. Она используется для аутентификации при определенных действиях. Она также используется для загрузки документов при необходимости. Местоположение устройства используется при выполнении определенных действий. Это может включать сбор фонового местоположения в зависимости от вашей программы контроля. Если вы зарегистрированы в BI SmartLink Tracking, это приложение собирает, передает и сохраняет местоположение устройств с заданными интервалами постоянно. Эта информация не передается третьим лицам. Если вы не зарегистрированы в BI SmartLink Tracking, сбор фонового местоположения не используется. Bluetooth может использоваться в зависимости от вашей программы контроля. Bluetooth используется только в сочетании с нашим оборудованием BI SmartBand. Устройства поблизости используются при проведении видеоконференций, чтобы внешние устройства, такие как динамики, можно было использовать с видеоконференцией.", "locationAlwaysPermissionRationale": "Этому приложению необходим постоянный доступ к вашему местоположению для соответствия требованиям вашей программы. Эти данные передаются и хранятся, но не передаются третьим лицам. Вам будет предложено включить разрешение «Разрешить все время»."}
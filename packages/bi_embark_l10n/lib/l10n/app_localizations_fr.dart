import 'package:intl/intl.dart' as intl;

import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get languageSelection => 'Sélection de la langue';

  @override
  String get selectALanguageToBegin => 'Sélectionnez une langue pour commencer';

  @override
  String get continueCommon => 'Continuer';

  @override
  String get arabic => 'arabe';

  @override
  String get somali => 'somali';

  @override
  String get urdu => 'ourdou';

  @override
  String get bengali => 'bengali';

  @override
  String get chineseSimplified => 'chinois (simplifié)';

  @override
  String get french => 'Français';

  @override
  String get haitianCreole => 'créole haïtien';

  @override
  String get hindi => 'hindi';

  @override
  String get portuguese => 'portugais';

  @override
  String get punjabi => 'Pendjabi';

  @override
  String get romanian => 'roumain';

  @override
  String get russian => 'russe';

  @override
  String get spanish => 'Espagnol';

  @override
  String get turkish => 'turc';

  @override
  String get ukrainian => 'ukrainien';

  @override
  String get vietnamese => 'vietnamien';

  @override
  String get termsAndConditions => 'Termes et conditions';

  @override
  String get back => 'Dos';

  @override
  String get acknowledge => 'Reconnaître';

  @override
  String get acknowledgeViewed => 'Remerciements réussis';

  @override
  String get acknowledgeViewedFailed => 'Échec de l\'accusé de réception';

  @override
  String get denverOmeletHistory =>
      'L\'histoire américaine a également son mot à dire sur la première préparation d\'une omelette. Le sandwich Denver, composé d\'une omelette prise en sandwich entre deux tranches de pain grillé, était une version antérieure de l\'omelette Denver. Ce sandwich est devenu populaire vers 1900, et c\'est plus tard que sa version omelette a été découverte. Selon la tradition, les œufs apportés par un chariot avaient un goût de renfermé et l\'omelette Denver était préparée pour atténuer ce goût désagréable. La recette de l\'omelette Denver se compose d\'oignons, de fromage, de jambon coupé en dés, de poivron vert, de champignons et, bien sûr, d\'œufs.';

  @override
  String get enterUsernamePassword =>
      'Entrez votre nom d\'utilisateur et votre mot de passe';

  @override
  String get username => 'Nom d\'utilisateur';

  @override
  String get password => 'Mot de passe';

  @override
  String get login => 'Se connecter';

  @override
  String get resetPassword => 'Réinitialiser le mot de passe';

  @override
  String get resetPasswordPopUpText =>
      'Êtes-vous sûr de vouloir réinitialiser le mot de passe ?';

  @override
  String get yes => 'Oui';

  @override
  String get no => 'Non';

  @override
  String get helloWelcome => 'Bonjour, bienvenue !';

  @override
  String toDoTasks(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Tâches à effectuer ( $countString )',
      zero: 'Tâches à effectuer',
    );
    return '$_temp0';
  }

  @override
  String get completed => 'Complété';

  @override
  String get home => 'Maison';

  @override
  String get selfReport => 'Auto-évaluation';

  @override
  String get notificationHistory => 'Historique des notifications';

  @override
  String get settings => 'Paramètres';

  @override
  String get logOut => 'Se déconnecter';

  @override
  String get cancel => 'Annuler';

  @override
  String get send => 'Envoyer';

  @override
  String get addComment => 'Ajouter un commentaire';

  @override
  String get youHaveNothingToComplete => 'Vous n\'avez rien à compléter';

  @override
  String get joinVideoConference => 'Rejoignez la vidéoconférence';

  @override
  String submitDocumentType(String documentType) {
    return 'Soumettre $documentType';
  }

  @override
  String get newMessage => 'Nouveau message';

  @override
  String get view => 'Voir';

  @override
  String get now => 'Maintenant';

  @override
  String get day => 'Jour';

  @override
  String get days => 'Jours';

  @override
  String get helpLayer =>
      'Nous avons traduit cette conversation de l\'anglais vers l\'espagnol. Vous pouvez désactiver la traduction à tout moment ou modifier votre langue préférée dans les paramètres.';

  @override
  String get messages => 'Messages';

  @override
  String get read => 'Lire';

  @override
  String get delivered => 'Livré';

  @override
  String fullWeekdayDate(DateTime date) {
    final intl.DateFormat dateDateFormat =
        intl.DateFormat.yMMMMEEEEd(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String get documentTypeTitle => 'Type de document';

  @override
  String documentTypeName(String documentType) {
    return '$documentType';
  }

  @override
  String get cannotOpenExternalApp =>
      'Aucune application ne peut effectuer cette action.';

  @override
  String get addPage => 'Ajouter une page';

  @override
  String get addDocument => 'Ajouter un document';

  @override
  String get myDocuments => 'Mes documents';

  @override
  String get noDocuments => 'Aucun document trouvé';

  @override
  String get cancelDocumentSubmissions => 'Voulez-vous annuler la soumission ?';

  @override
  String documentCaptureInstructions(int maximum_count) {
    return 'Prenez une photo du document que vous souhaitez envoyer. Si votre document comporte plusieurs pages, prenez une photo par page, jusqu\'à $maximum_count pages.';
  }

  @override
  String get type => 'Taper';

  @override
  String get appointmentName => 'Nom du rendez-vous';

  @override
  String get appointmentComments => 'Commentaires sur le rendez-vous';

  @override
  String get appointmentRecurrenceType => 'Type de récurrence de rendez-vous';

  @override
  String get recurrence => 'Récurrence';

  @override
  String get recurrenceDaysOfWeek => 'Jours de récurrence de la semaine';

  @override
  String get recurrsEvery => 'Se reproduit toutes les';

  @override
  String get recurrenceDaysOfWeekErrorText =>
      'Vous devez sélectionner au moins un jour de la semaine';

  @override
  String get dateAndTime => 'Date et heure';

  @override
  String get address => 'Adresse';

  @override
  String get phoneNumber => 'Numéro de téléphone';

  @override
  String get getDirections => 'Obtenir l\'itinéraire';

  @override
  String get call => 'Appel';

  @override
  String get noWebsiteAvailable => 'Aucun site Web disponible';

  @override
  String get requestScheduleChange => 'Demande de changement d\'horaire';

  @override
  String get requestScheduleDelete => 'Demande de suppression de planification';

  @override
  String get participants => 'Participants';

  @override
  String get authenticationFailure => 'Échec d\'authentification';

  @override
  String get invalidUsername => 'nom d\'utilisateur invalide';

  @override
  String get invalidPassword => 'mot de passe invalide';

  @override
  String get omelet => 'Omelette';

  @override
  String get forgotPassword => 'Mot de passe oublié';

  @override
  String get logIn => 'Se connecter';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get language => 'Langue';

  @override
  String get passwordResetSent =>
      'Un e-mail a été envoyé au nom d\'utilisateur fourni avec des instructions pour réinitialiser votre mot de passe.';

  @override
  String get permissionsMessage =>
      'BI SmartLINK nécessite certaines autorisations pour fonctionner correctement. Une connexion Internet est requise pour envoyer et recevoir des données depuis nos serveurs. La caméra est nécessaire à plusieurs fonctions. Elle sert à l\'authentification pour certaines activités et permet également de télécharger des documents si nécessaire. La localisation de l\'appareil est utilisée lors de certaines activités. Cela peut inclure une collecte de données en arrière-plan, selon votre programme de supervision. Si vous êtes inscrit à BI SmartLink Tracking, cette application collecte, transmet et stocke la localisation de l\'appareil à intervalles réguliers. Ces informations ne sont pas partagées avec des tiers. Si vous n\'êtes pas inscrit à BI SmartLink Tracking, la collecte de données en arrière-plan n\'est pas utilisée. Le Bluetooth peut être utilisé selon votre programme de supervision. Le Bluetooth est utilisé uniquement lorsqu\'il est couplé à notre équipement BI SmartBand. Les appareils à proximité sont utilisés lors des visioconférences afin que des périphériques externes, tels que des haut-parleurs, puissent être utilisés.';

  @override
  String xNumberOfDays(int x) {
    return '$x jours';
  }

  @override
  String get oneDay => '1 jour';

  @override
  String get oneHour => '1 heure';

  @override
  String xNumberOfHours(int x) {
    return '$x heures';
  }

  @override
  String get today => 'Aujourd\'hui';

  @override
  String get tomorrow => 'Demain';

  @override
  String get refresh => 'Rafraîchir';

  @override
  String get noItemsToDisplay => 'Aucun élément à afficher';

  @override
  String get week => 'Semaine';

  @override
  String get weeks => 'Semaines';

  @override
  String get month => 'Mois';

  @override
  String get list => 'Liste';

  @override
  String get to => 'à';

  @override
  String dateRange(DateTime startDate, DateTime endDate) {
    final intl.DateFormat startDateDateFormat = intl.DateFormat.yMd(localeName);
    final String startDateString = startDateDateFormat.format(startDate);
    final intl.DateFormat endDateDateFormat = intl.DateFormat.yMd(localeName);
    final String endDateString = endDateDateFormat.format(endDate);

    return '$startDateString à $endDateString';
  }

  @override
  String date(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yMd(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String dateMonthYear(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yM(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String dateWeekday(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.EEEE(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String dateHourOfDay(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.j(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String get standInTitle => 'Titre de remplacement';

  @override
  String get appointmentChange => 'Changement de rendez-vous';

  @override
  String get requestAppointment => 'Demander un rendez-vous';

  @override
  String get calendar => 'Calendrier';

  @override
  String get notImplemented => 'Non mis en œuvre';

  @override
  String get requestSubmitted => 'Demande soumise';

  @override
  String get submit => 'Soumettre';

  @override
  String get requestToDelete => 'Demande de suppression';

  @override
  String get submitRequestFailed => 'La soumission de la demande a échoué';

  @override
  String get noMessages => 'Vous n\'avez aucun message';

  @override
  String get translationMessage =>
      'Nous avons traduit cette conversation. Vous pouvez désactiver la traduction à tout moment ou modifier votre langue préférée dans les paramètres.';

  @override
  String get dontAskAgain => 'Ne demandez plus';

  @override
  String get okay => 'D\'accord';

  @override
  String get ok => 'D\'accord';

  @override
  String get startTime => 'Heure de début';

  @override
  String get endTime => 'Fin des temps';

  @override
  String get startDate => 'Date de début';

  @override
  String get endDate => 'Date de fin';

  @override
  String get biometrics => 'Biométrie';

  @override
  String get privacyPolicy => 'politique de confidentialité';

  @override
  String get terms => 'Termes et conditions';

  @override
  String get submitFeedback => 'Soumettre des commentaires';

  @override
  String get profile => 'Profil';

  @override
  String get preferredLanguage => 'Langue préférée';

  @override
  String get mondayShort => 'L';

  @override
  String get tuesdayShort => 'M';

  @override
  String get wednesdayShort => 'M';

  @override
  String get thursdayShort => 'J';

  @override
  String get fridayShort => 'V';

  @override
  String get saturdayShort => 'S';

  @override
  String get sundayShort => 'D';

  @override
  String get oneTime => 'Une fois';

  @override
  String get daily => 'Tous les jours';

  @override
  String get weekly => 'Hebdomadaire';

  @override
  String get debugTools => 'OUTILS DE DÉBOGAGE';

  @override
  String get privacyPolicyTitle => 'politique de confidentialité';

  @override
  String get appointmentInPastValidation =>
      'Un rendez-vous ne peut pas être demandé dans le passé';

  @override
  String get fieldRequired => 'Requis';

  @override
  String get maxDate => 'Date maximale';

  @override
  String pendingTasks(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Tâches en attente ( $countString )',
      zero: 'Tâches en attente',
    );
    return '$_temp0';
  }

  @override
  String xMinutesAgo(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'il y a $countString minutes',
      one: 'il y a 1 minute',
      zero: 'Maintenant',
    );
    return '$_temp0';
  }

  @override
  String xMinutesInFuture(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Dans $countString minutes',
      one: 'Dans 1 minute',
      zero: 'Maintenant',
    );
    return '$_temp0';
  }

  @override
  String xHoursAgo(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'il y $countString heures',
      one: 'il y a 1 heure',
      zero: 'Maintenant',
    );
    return '$_temp0';
  }

  @override
  String xHoursInFuture(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Dans $countString heures',
      one: 'Dans 1 heure',
      zero: 'Maintenant',
    );
    return '$_temp0';
  }

  @override
  String xDaysAgo(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'il y a $countString jours',
      one: 'il y a 1 jour',
      zero: 'Maintenant',
    );
    return '$_temp0';
  }

  @override
  String xDaysInFuture(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Dans $countString jours',
      one: 'Demain',
      zero: 'Aujourd\'hui',
    );
    return '$_temp0';
  }

  @override
  String get join => 'Rejoindre';

  @override
  String get reply => 'Répondre';

  @override
  String get contactInformationLabel => 'Coordonnées';

  @override
  String get agencyPhoneNumberLabel => 'Numéro de téléphone de l\'agence';

  @override
  String get agencyAddressLabel => 'Adresse de l\'agence';

  @override
  String get residenceInformationLabel => 'Informations sur la résidence';

  @override
  String get reload => 'Recharger';

  @override
  String get primary => 'Primaire';

  @override
  String get noTransportation => 'Pas de transport';

  @override
  String get addTransportation => 'Ajouter un transport';

  @override
  String get filter => 'Filtre';

  @override
  String get all => 'Tous';

  @override
  String get transportation => 'Transport';

  @override
  String get unknown => 'Inconnu';

  @override
  String get genericError => 'Une erreur s\'est produite';

  @override
  String get comments => 'Commentaires';

  @override
  String get state => 'État';

  @override
  String get color => 'Couleur';

  @override
  String get make => 'Faire';

  @override
  String get model => 'Modèle';

  @override
  String get modelYear => 'Année du modèle';

  @override
  String get plate => 'Plaque';

  @override
  String get errorModelYear => 'Veuillez saisir une année valide à 4 chiffres.';

  @override
  String get clientOwned => 'Propriété du client';

  @override
  String get primaryTransportation => 'Transport primaire';

  @override
  String get tooLong => 'Trop long';

  @override
  String get appointmentDetailsTitle => 'Détails du rendez-vous';

  @override
  String get website => 'Site web';

  @override
  String get viewWebsite => 'Voir le site Web';

  @override
  String get appointmentDateAndTime => 'Date et heure du rendez-vous';

  @override
  String get websiteGeneralError => 'Erreur lors de l\'accès au site Web';

  @override
  String get phoneGeneralError => 'Erreur lors de l\'appel';

  @override
  String get deleteTransporationConfirm =>
      'Êtes-vous sûr de vouloir supprimer votre moyen de transport ?';

  @override
  String addressN(num n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
    );
    final String nString = nNumberFormat.format(n);

    return 'Adresse $nString';
  }

  @override
  String get timeZone => 'Fuseau horaire';

  @override
  String get city => 'Ville';

  @override
  String get invalidInputFormat => 'Format invalide';

  @override
  String get noEndDate => 'Pas de date de fin';

  @override
  String get appointmentRecurrsEvery => 'Le rendez-vous revient tous les';

  @override
  String get deleteEmploymentConfirm =>
      'Êtes-vous sûr de vouloir supprimer votre emploi ?';

  @override
  String get employment => 'Emploi';

  @override
  String get noEmployment => 'Pas d\'emploi';

  @override
  String get addEmployment => 'Ajouter un emploi';

  @override
  String get employer => 'Employeur';

  @override
  String get fax => 'Fax';

  @override
  String get jobTitle => 'Titre d\'emploi';

  @override
  String get supervisor => 'Superviseur';

  @override
  String get hoursPerWeek => 'Heures par semaine';

  @override
  String get schedule => 'Calendrier';

  @override
  String get initialHourlyPay => 'Salaire horaire initial';

  @override
  String get finalHourlyPay => 'Salaire horaire final';

  @override
  String get emailLabel => 'E-mail';

  @override
  String get primaryPhoneLabel => 'Numéro de téléphone principal';

  @override
  String get homePhoneLabel => 'Numéro de domicile';

  @override
  String get mobilePhoneLabel => 'Numéro de portable';

  @override
  String get workPhoneLabel => 'Numéro de travail';

  @override
  String get workPhoneLabelSelection => 'Travail';

  @override
  String get mobilePhoneSelectionLabel => 'Mobile';

  @override
  String get primaryPhoneSelectionBlank =>
      'Le numéro de téléphone principal ne peut pas être vide';

  @override
  String get zipCode => 'Code postal';

  @override
  String get county => 'Comté';

  @override
  String get personalVehicle => 'Véhicule personnel';

  @override
  String get publicTransportation => 'Transports en commun';

  @override
  String get personalContact => 'Contact personnel';

  @override
  String get taxi => 'Taxi';

  @override
  String get bicycle => 'Vélo';

  @override
  String get walk => 'Marcher';

  @override
  String get otherSpecify => 'Autre (préciser)';

  @override
  String get notes => 'Remarques';

  @override
  String get personalContacts => 'Contacts personnels';

  @override
  String get additional => 'Supplémentaire';

  @override
  String get response => 'Réponse';

  @override
  String get selfReportSubmitted => 'Auto-rapport soumis !';

  @override
  String get selfReportError => 'Erreur lors de l\'envoi de l\'auto-rapport !';

  @override
  String get serverError =>
      'Une erreur interne s\'est produite lors du traitement de votre demande. Veuillez contacter votre responsable hiérarchique.';

  @override
  String get firstName => 'Prénom';

  @override
  String get lastName => 'Nom de famille';

  @override
  String get middleInitial => 'Initiale du milieu';

  @override
  String get citizenship => 'Citoyenneté';

  @override
  String get dateOfBirth => 'Date de naissance';

  @override
  String get other => 'Autre';

  @override
  String get parent => 'Mère';

  @override
  String get spouse => 'Conjoint';

  @override
  String get exSpouse => 'Ex-conjoint';

  @override
  String get child => 'Enfant';

  @override
  String get sibling => 'Frère et sœur';

  @override
  String get grandparent => 'Grand-parent';

  @override
  String get extendedFamily => 'Famille élargie';

  @override
  String get friend => 'Ami';

  @override
  String get attorney => 'Avocat';

  @override
  String get inLaw => 'En droit';

  @override
  String get guardian => 'Tuteur';

  @override
  String get liveInFriend => 'Vivre en ami';

  @override
  String get victim => 'Victime';

  @override
  String get childsFather => 'Père de l\'enfant';

  @override
  String get childAsContact => 'L\'enfant comme contact';

  @override
  String get description => 'Description';

  @override
  String get livesWithClient => 'Vit avec le client';

  @override
  String get isProBono => 'Bénévolat';

  @override
  String get dependentOfClient => 'Dépendant du client';

  @override
  String get noPersonalContact => 'Aucun contact personnel';

  @override
  String get addPersonalContact => 'Ajouter un contact personnel';

  @override
  String get deletePeronalContactConfirm =>
      'Êtes-vous sûr de vouloir supprimer votre contact personnel ?';

  @override
  String get deleteAppointmentConfirm =>
      'Êtes-vous sûr de vouloir supprimer votre rendez-vous ?';

  @override
  String get failedToLoadCalendar =>
      'Échec du chargement des éléments du calendrier. Veuillez réessayer ou contacter votre responsable.';

  @override
  String get invalidDate => 'Date invalide';

  @override
  String get communityReferral => 'Orientation communautaire';

  @override
  String get status => 'Statut';

  @override
  String get appointmentTime => 'Heure du rendez-vous';

  @override
  String get appointmentRecurrence => 'Récurrence de rendez-vous';

  @override
  String get appointmentNoChangesMade =>
      'Aucune modification n\'a été apportée aux informations. Veuillez les modifier pour les soumettre.';

  @override
  String get appointmentNoChangesMadeTitle => 'Aucun changement';

  @override
  String get noInternetConnection => 'Pas de connexion Internet';

  @override
  String get configurationUpdated => 'Configuration mise à jour';

  @override
  String get pending => 'En attente';

  @override
  String get ongoing => 'En cours';

  @override
  String get utilized => 'Utilisé';

  @override
  String get notUtilized => 'Non utilisé';

  @override
  String get instructions => 'Instructions';

  @override
  String get contactNotes => 'Notes de contact';

  @override
  String get none => 'Aucun';

  @override
  String get resources => 'Ressources';

  @override
  String get documents => 'Documents';

  @override
  String get documentsSubmited => 'Document soumis !';

  @override
  String get beginSetup => 'Commencer la configuration';

  @override
  String get mdmAppPermissionNotGranted =>
      'L\'application MDM Plugin ne dispose pas de l\'autorisation Téléphone. Veuillez vérifier les autorisations de l\'application MDM Plugin ou la configuration Samsung Knox.';

  @override
  String get phoneNotAssigned => 'Téléphone non attribué';

  @override
  String get missingMdmPhoneNumber =>
      'Impossible de déterminer le numéro de téléphone. Veuillez vous assurer qu\'une carte SIM est installée et réessayer.';

  @override
  String get hours => 'Heures';

  @override
  String get name => 'Nom';

  @override
  String get resource => 'Ressource';

  @override
  String get businessHours => 'Heures d\'ouverture';

  @override
  String get badLink => 'Le lien fourni ne peut pas être résolu';

  @override
  String get endMeeting => 'FIN DE LA RÉUNION';

  @override
  String get videoLoadingMessage => 'Connexion à la salle...';

  @override
  String get emptyVideoRoomMessage =>
      'En attente qu\'un autre participant se connecte à la salle...';

  @override
  String get nearByDevices => 'Appareils à proximité';

  @override
  String get camera => 'Caméra';

  @override
  String get microphone => 'Microphone';

  @override
  String get location => 'Emplacement';

  @override
  String get permissionDialogTitle =>
      'Autorisations nécessaires pour continuer ';

  @override
  String get noAvailableVCErrorMessage =>
      'Aucun appel disponible pour le moment';

  @override
  String get sessionExpired => 'La session a expiré';

  @override
  String get accept => 'Accepter';

  @override
  String get retake => 'Reprendre';

  @override
  String get usePhoto => 'Utiliser une photo';

  @override
  String upload(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Télécharger ( $count )',
    );
    return '$_temp0';
  }

  @override
  String get vcDialogTitle => 'Appel vidéoconférence entrant';

  @override
  String get enrollment => 'Inscription';

  @override
  String get guidelines => 'Lignes directrices';

  @override
  String get fullFrameFace => 'Visage entièrement encadré';

  @override
  String get simpleBackground => 'Arrière-plan simple';

  @override
  String get neutralExpression => 'Expression neutre';

  @override
  String get levelCamera => 'Caméra alignée à niveau';

  @override
  String get optimalDistance => 'Distance optimale';

  @override
  String get goodLighting => 'Bon éclairage';

  @override
  String get doNot => 'Ne pas ';

  @override
  String get noSmile => 'sourire, tourner la tête ou fermer les yeux';

  @override
  String get noOtherPeople => 'inclure plusieurs personnes ou animaux';

  @override
  String get extendArm => 'redressez ou étendez votre bras';

  @override
  String get noFunnyFaces =>
      'utiliser des lèvres de canard ou des angles étranges';

  @override
  String get wearSunglasses => 'porter des lunettes de soleil';

  @override
  String get noCamerasAvaliable =>
      'Il semble qu\'aucune caméra ne soit disponible. Veuillez fermer l\'application et réessayer.';

  @override
  String get callHistory => 'Historique des appels';

  @override
  String get callHistoryLoadingMessage =>
      'Chargement de votre historique d\'appels...';

  @override
  String get settingPin => 'Configurez votre code PIN';

  @override
  String get enterYourPin => 'Entrez votre code PIN';

  @override
  String get reEnterYourPin => 'Confirmez votre code PIN';

  @override
  String get pinsDoNotMatch => 'Code PIN non concordant. Réessayez.';

  @override
  String get pinSetupComplete => 'Configuration du code PIN terminée';

  @override
  String get incorrectPin => 'Code PIN incorrect';

  @override
  String get resetPin => 'Réinitialiser le code PIN';

  @override
  String get pinResetQuestion =>
      'Souhaitez-vous réinitialiser votre code PIN ?';

  @override
  String get tryingToResetPin => 'Tentative de réinitialisation du code PIN';

  @override
  String get pinResetSuccess => 'Réinitialisation du code PIN réussie';

  @override
  String get pinResetRequestFailed =>
      'Échec de la réinitialisation du code PIN. Veuillez réessayer.';

  @override
  String get forgotPin => 'Code PIN oublié';

  @override
  String get waitingForVerification => 'En attente de vérification';

  @override
  String get waitingForVerificationTrailing => 'En attente de vérification...';

  @override
  String get enrollmentComplete => 'Inscription soumise !';

  @override
  String get submittingEnrollment => 'Soumission de l\'inscription';

  @override
  String get enrollmentError =>
      'Une erreur s\'est produite lors de l\'inscription. Veuillez consulter les instructions et réessayer.';

  @override
  String get enrollmentDescription =>
      'L\'inscription nécessite une série de photos de vous. Cliquez sur « Continuer » pour ouvrir la vue de l\'appareil photo. Centrez votre visage dans le contour et attendez que l\'appareil photo prenne votre photo.';

  @override
  String get callHistoryBannerError =>
      'Échec du chargement de votre historique d\'appels. Veuillez réessayer ou contacter votre superviseur.';

  @override
  String get checkIn => 'Enregistrement';

  @override
  String get uploadLogs => 'Télécharger les journaux';

  @override
  String get phoneCallDialogTitle => 'Appel entrant';

  @override
  String get callInProgress => 'Appel en cours';

  @override
  String get openPhoneAccountSettings =>
      'Veuillez ouvrir l\'enregistrement BI SmartLink dans les paramètres de votre compte téléphonique,';

  @override
  String get dismiss => 'Rejeter';

  @override
  String get report => 'Rapport';

  @override
  String get submittingCheckIn => 'Soumission de l\'enregistrement';

  @override
  String get sessionExpiredMessage =>
      'Votre session a expiré. Veuillez vous reconnecter pour continuer à utiliser l\'application.';

  @override
  String get updateRequired => 'Mise à jour requise';

  @override
  String get installUpdate => 'Installer la mise à jour';

  @override
  String get callerHungUpMessage => 'L\'appelant a raccroché.';

  @override
  String get emptyCallHistoryMessage => 'Aucun appel récent';

  @override
  String get voipCallPermissionsDialogMessage =>
      'Pour garantir une fonctionnalité complète, veuillez activer les autorisations d\'appel VoIP dans vos paramètres.';

  @override
  String noCheckInsAllowed(String futureDate) {
    return 'Aucun enregistrement n\'est autorisé avant $futureDate sauf si vous recevez une notification vous invitant à en effectuer un.';
  }

  @override
  String get biometricCheckInDue => 'Enregistrement biométrique dû';

  @override
  String get selfReportCheckInDue =>
      'Date limite d\'enregistrement de l\'auto-évaluation';

  @override
  String get newCalendarItem => 'Nouvel élément de calendrier';

  @override
  String get newDocument => 'Nouveau document';

  @override
  String get newVideoConference => 'Nouvelle vidéoconférence';

  @override
  String get calendarChangeRequestSubmitted =>
      'Demande de changement soumise !';

  @override
  String get requestNotificationResponse =>
      'Vous recevrez une notification lorsque votre agent répondra.';

  @override
  String get permissionRequired => 'Autorisation requise';

  @override
  String get permissionRequiredToUseThisPlugin =>
      'Autorisation requise pour utiliser ce plugin.';

  @override
  String get street => 'Rue';

  @override
  String withColonSuffixAndSpaces(String value) {
    return '$value : ';
  }

  @override
  String get warning => 'Avertissement';

  @override
  String get enableLocation =>
      'Les services de localisation de l\'appareil sont actuellement désactivés. Veuillez les activer pour l\'application. ';

  @override
  String get streetName => 'Nom de la rue';

  @override
  String get genericChangeRequestSubmitted => 'Demande de changement soumise !';

  @override
  String withSpaceDashShapce(String value) {
    return '$value - ';
  }

  @override
  String prefixWithProfile(String value) {
    return 'Profil - $value';
  }

  @override
  String get vcHungUpMessage => 'Aucun appel disponible pour le moment.';

  @override
  String get profileNoContacts => 'Aucun contact';

  @override
  String get relationship => 'Relation';

  @override
  String get checkInDescription =>
      'Veuillez aligner votre visage sur le contour tout en tenant votre téléphone à hauteur des yeux. Votre photo sera prise automatiquement.';

  @override
  String get checkInComplete => 'Enregistrement effectué !';

  @override
  String get takePhoto => 'Prendre une photo';

  @override
  String get referral => 'Orientation';

  @override
  String get restartPhone => 'Veuillez redémarrer le téléphone';

  @override
  String get gettingStarted => 'Commencer';

  @override
  String get newFeatures =>
      'SmartLINK Nouvelle conception et nouvelles fonctionnalités';

  @override
  String get done => 'Fait';

  @override
  String get next => 'Suivant';

  @override
  String get previous => 'Précédent';

  @override
  String get todoTasksTitle => 'Tâches à effectuer';

  @override
  String get hamburgerMenu => 'Éléments du menu Hamburger';

  @override
  String get myInfo => 'Le profil remplace Mes informations';

  @override
  String get dashboardTabs => 'Page d\'accueil';

  @override
  String get accessCheckIn => 'Enregistrement d\'accès';

  @override
  String get close => 'Fermer';

  @override
  String get enrollmentCompletePNTitle => 'Inscription terminée';

  @override
  String get fixedBiometricPNTitle => 'Enregistrement biométrique';

  @override
  String get bluetoothDisabled => 'Bluetooth désactivé';

  @override
  String get enableInSettings => 'Activer dans les paramètres';

  @override
  String get locationServiceDisabled => 'Services de localisation désactivés';

  @override
  String get enableLocationTodoItemTitle =>
      'Services de localisation désactivés';

  @override
  String get appName => 'BI SmartLINK';

  @override
  String get trackingRunningInBackground =>
      'BI SmartLINK surveille votre emplacement en arrière-plan pour garantir la conformité du programme.';

  @override
  String get centerFace => 'Centrez votre visage dans le contour';

  @override
  String get blinkAndTakeBreath => 'Clignez des yeux et respirez profondément';

  @override
  String get waitUpto15Seconds => 'Attendez jusqu\'à 15 secondes';

  @override
  String get autoCapture => 'Votre photo sera capturée automatiquement';

  @override
  String get takeOff =>
      'Enlevez vos lunettes, votre chapeau ou tout ce qui couvre votre visage';

  @override
  String get useNeutralExpression => 'Utilisez une expression neutre';

  @override
  String get wellLit => 'Assurez-vous que votre visage est bien éclairé';

  @override
  String get joiningVideoConferenceDialogTitle => 'Adhésion...';

  @override
  String get preferences => 'Préférences';

  @override
  String get legal => 'Légal';

  @override
  String get smartband => 'Bracelet intelligent';

  @override
  String get firmwareVersion => 'Version du micrologiciel';

  @override
  String get smartbandId => 'SmartBand ID';

  @override
  String get regulatoryLabels => 'Étiquettes réglementaires';

  @override
  String get rfExposure => 'Exposition aux RF';

  @override
  String get rfExposureTitle => 'Débit d\'absorption spécifique (DAS)';

  @override
  String get rfExposureParagraph1 =>
      'Cet équipement est conforme aux directives internationales relatives à l\'exposition aux radiofréquences. Il est conçu pour ne pas dépasser les limites d\'exposition aux radiofréquences recommandées par les directives internationales. Ces directives, élaborées par un organisme scientifique indépendant (ICNIRP), prévoient une marge de sécurité importante destinée à garantir la sécurité de tous, quels que soient leur âge et leur état de santé.';

  @override
  String get rfExposureParagraph2 =>
      'La limite DAS pour les appareils mobiles est de 1,6 W/kg avec une distance de séparation comme suit :';

  @override
  String get rfExposureHead => 'Tête – 0 mm';

  @override
  String get rfExposureBody => 'Corps – 10 mm';

  @override
  String get designedTested => 'Conçu, testé et assemblé aux États-Unis';

  @override
  String get smartbandColor => 'Couleur : Noir/Bleu';

  @override
  String get notAssigned => 'Non attribué';

  @override
  String appSettingsVersionText(String version) {
    return 'Version $version';
  }

  @override
  String fccId(String fccId) {
    return 'Contient l\'ID FCC : $fccId';
  }

  @override
  String icId(String isedId) {
    return 'Contient l\'identifiant ISED : $isedId';
  }

  @override
  String biRegulatoryModel(String model) {
    return 'MODÈLE BI : $model';
  }

  @override
  String get goToSettings => 'Accédez aux paramètres';

  @override
  String get enableBluetooth =>
      'Activez Bluetooth pour vous connecter à votre appareil SmartBand.';

  @override
  String get bluetooth => 'Bluetooth';

  @override
  String get locationAlwaysPermissionRationale =>
      'Cette application a besoin d\'accéder à votre localisation à tout moment pour respecter les exigences de votre programme. Ces données sont transmises et stockées, mais ne sont pas partagées avec des tiers. Vous serez invité à activer l\'autorisation « Autoriser tout le temps ».';

  @override
  String get checkingForFotaMessage =>
      'Vérification de la disponibilité du SmartBand FOTA...';

  @override
  String checkingForSpecificFotaMessage(String version) {
    return 'Vérification de la version SmartBand FOTA $version ...';
  }

  @override
  String get smartBandUpdateComplete => 'Mise à jour du SmartBand terminée !';

  @override
  String get smartBandUpdateFailed =>
      'Échec de la mise à jour du micrologiciel du SmartBand.';

  @override
  String get smartBandUpdateInvalidFirmwareFile =>
      'Le fichier FOTA n\'est pas valide. Assurez-vous que la version existe et est correcte avant de réessayer.';

  @override
  String get checkForSmartBandFota => 'Vérifiez SmartBand FOTA';

  @override
  String get smartBandFotaVersionToDownload =>
      'Version SmartBand FOTA à télécharger';

  @override
  String get notificationPermissionRationale =>
      'Cette application vous enverra des mises à jour pour vous aider à respecter les exigences de votre programme. Veuillez autoriser l\'accès dans la boîte de dialogue suivante.';

  @override
  String get genericPermissionRationale =>
      'Cette application nécessite une autorisation supplémentaire pour répondre aux exigences de votre programme. Veuillez l\'autoriser dans la boîte de dialogue suivante.';
}

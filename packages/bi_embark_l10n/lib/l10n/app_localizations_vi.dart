import 'package:intl/intl.dart' as intl;

import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get languageSelection => 'Lựa chọn ngôn ngữ';

  @override
  String get selectALanguageToBegin => 'Chọn ngôn ngữ để bắt đầu';

  @override
  String get continueCommon => 'Tiếp tục';

  @override
  String get arabic => 'Tiếng Ả Rập';

  @override
  String get somali => 'Tiếng Somali';

  @override
  String get urdu => 'Tiếng Urdu';

  @override
  String get bengali => 'Tiếng Bengal';

  @override
  String get chineseSimplified => 'Tiếng Trung (giản thể)';

  @override
  String get french => 'Tiếng Pháp';

  @override
  String get haitianCreole => 'Tiếng Creole Haiti';

  @override
  String get hindi => 'Tiếng Hin-ddi';

  @override
  String get portuguese => 'Tiếng Bồ Đào Nha';

  @override
  String get punjabi => 'Tiếng Punjab';

  @override
  String get romanian => 'Tiếng Rumani';

  @override
  String get russian => 'Tiếng Nga';

  @override
  String get spanish => 'Tiếng Tây Ban Nha';

  @override
  String get turkish => 'Thổ Nhĩ Kỳ';

  @override
  String get ukrainian => 'Tiếng Ukraina';

  @override
  String get vietnamese => 'Tiếng Việt';

  @override
  String get termsAndConditions => 'Điều khoản và điều kiện';

  @override
  String get back => 'Mặt sau';

  @override
  String get acknowledge => 'Thừa nhận';

  @override
  String get acknowledgeViewed => 'Xác nhận thành công';

  @override
  String get acknowledgeViewedFailed => 'Xác nhận không thành công';

  @override
  String get denverOmeletHistory => 'Liên quan đến cách chế biến trứng ốp la đầu tiên, lịch sử nước Mỹ cũng có phần của nó. Bánh sandwich Denver, gồm một quả trứng ốp la kẹp giữa hai lát bánh mì nướng, là phiên bản trước đó của Trứng ốp la Denver. Bánh sandwich Denver trở nên phổ biến vào khoảng năm 1900 và sau đó phiên bản trứng ốp la của nó mới được phát hiện. Theo câu chuyện truyền thống, trứng được chở bằng xe ngựa có mùi ôi thiu và trứng ốp la Denver được chế biến để ức chế mùi khó chịu đó. Công thức làm trứng ốp la Denver bao gồm hành tây, phô mai, giăm bông thái hạt lựu, ớt xanh, nấm; và tất nhiên là trứng.';

  @override
  String get enterUsernamePassword => 'Nhập tên người dùng và mật khẩu của bạn';

  @override
  String get username => 'Tên người dùng';

  @override
  String get password => 'Mật khẩu';

  @override
  String get login => 'Đăng nhập';

  @override
  String get resetPassword => 'Đặt lại mật khẩu';

  @override
  String get resetPasswordPopUpText => 'Bạn có chắc chắn muốn đặt lại mật khẩu không?';

  @override
  String get yes => 'Đúng';

  @override
  String get no => 'KHÔNG';

  @override
  String get helloWelcome => 'Xin chào, chào mừng!';

  @override
  String toDoTasks(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Nhiệm vụ cần làm ( $countString )',
      zero: 'Nhiệm vụ cần làm',
    );
    return '$_temp0';
  }

  @override
  String get completed => 'Hoàn thành';

  @override
  String get home => 'Trang chủ';

  @override
  String get selfReport => 'Tự báo cáo';

  @override
  String get notificationHistory => 'Lịch sử thông báo';

  @override
  String get settings => 'Cài đặt';

  @override
  String get logOut => 'Đăng xuất';

  @override
  String get cancel => 'Hủy bỏ';

  @override
  String get send => 'Gửi';

  @override
  String get addComment => 'Thêm bình luận';

  @override
  String get youHaveNothingToComplete => 'Bạn không có gì để hoàn thành';

  @override
  String get joinVideoConference => 'Tham gia Hội nghị truyền hình';

  @override
  String submitDocumentType(String documentType) {
    return 'Gửi $documentType';
  }

  @override
  String get newMessage => 'Tin nhắn mới';

  @override
  String get view => 'Xem';

  @override
  String get now => 'Hiện nay';

  @override
  String get day => 'Ngày';

  @override
  String get days => 'Ngày';

  @override
  String get helpLayer => 'Chúng tôi đã dịch cuộc trò chuyện này từ tiếng Anh sang tiếng Tây Ban Nha. Bạn có thể tắt bản dịch này bất kỳ lúc nào hoặc thay đổi ngôn ngữ ưa thích của bạn trong Cài đặt.';

  @override
  String get messages => 'Tin nhắn';

  @override
  String get read => 'Đọc';

  @override
  String get delivered => 'Đã giao hàng';

  @override
  String fullWeekdayDate(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yMMMMEEEEd(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String get documentTypeTitle => 'Loại tài liệu';

  @override
  String documentTypeName(String documentType) {
    return '$documentType';
  }

  @override
  String get cannotOpenExternalApp => 'Không có ứng dụng nào có thể thực hiện hành động này.';

  @override
  String get addPage => 'Thêm Trang';

  @override
  String get addDocument => 'Thêm tài liệu';

  @override
  String get myDocuments => 'Tài liệu của tôi';

  @override
  String get noDocuments => 'Không tìm thấy tài liệu nào';

  @override
  String get cancelDocumentSubmissions => 'Bạn có muốn hủy việc nộp bài không?';

  @override
  String documentCaptureInstructions(int maximum_count) {
    return 'Chụp ảnh tài liệu bạn muốn gửi. Nếu tài liệu của bạn có nhiều trang, hãy chụp một ảnh cho mỗi trang, tối đa $maximum_count trang.';
  }

  @override
  String get type => 'Kiểu';

  @override
  String get appointmentName => 'Tên cuộc hẹn';

  @override
  String get appointmentComments => 'Bình luận về cuộc hẹn';

  @override
  String get appointmentRecurrenceType => 'Loại tái hẹn';

  @override
  String get recurrence => 'Sự tái diễn';

  @override
  String get recurrenceDaysOfWeek => 'Ngày lặp lại trong tuần';

  @override
  String get recurrsEvery => 'Lặp lại mỗi';

  @override
  String get recurrenceDaysOfWeekErrorText => 'Bạn phải chọn ít nhất một ngày trong tuần';

  @override
  String get dateAndTime => 'Ngày & Giờ';

  @override
  String get address => 'Địa chỉ';

  @override
  String get phoneNumber => 'Số điện thoại';

  @override
  String get getDirections => 'Nhận chỉ đường';

  @override
  String get call => 'Gọi';

  @override
  String get noWebsiteAvailable => 'Không có trang web nào khả dụng';

  @override
  String get requestScheduleChange => 'Yêu cầu thay đổi lịch trình';

  @override
  String get requestScheduleDelete => 'Yêu cầu lịch trình xóa';

  @override
  String get participants => 'Người tham gia';

  @override
  String get authenticationFailure => 'Xác thực không thành công';

  @override
  String get invalidUsername => 'tên người dùng không hợp lệ';

  @override
  String get invalidPassword => 'mật khẩu không hợp lệ';

  @override
  String get omelet => 'Trứng ốp la';

  @override
  String get forgotPassword => 'Quên mật khẩu';

  @override
  String get logIn => 'Đăng nhập';

  @override
  String get selectLanguage => 'Chọn ngôn ngữ';

  @override
  String get language => 'Ngôn ngữ';

  @override
  String get passwordResetSent => 'Một email đã được gửi đến tên người dùng kèm theo hướng dẫn đặt lại mật khẩu của bạn.';

  @override
  String get permissionsMessage => 'BI SmartLINK cần một số quyền nhất định để hoạt động bình thường. Cần có kết nối internet để gửi và nhận dữ liệu từ máy chủ của chúng tôi. Camera cần thiết cho một số chức năng. Camera được sử dụng để xác thực cho một số hoạt động nhất định. Camera cũng được sử dụng để tải lên tài liệu khi cần. Vị trí của thiết bị được sử dụng khi thực hiện một số hoạt động nhất định. Điều này có thể bao gồm thu thập vị trí nền tùy thuộc vào chương trình giám sát của bạn. Nếu bạn đã đăng ký BI SmartLink Tracking, ứng dụng này sẽ thu thập, truyền và lưu trữ vị trí của thiết bị theo các khoảng thời gian đã đặt liên tục. Thông tin này không được chia sẻ với bên thứ ba. Nếu bạn không đăng ký BI SmartLink Tracking, việc thu thập vị trí nền sẽ không được sử dụng. Có thể sử dụng Bluetooth tùy thuộc vào chương trình giám sát của bạn. Bluetooth chỉ được sử dụng khi kết hợp với thiết bị BI SmartBand của chúng tôi. Các thiết bị ở gần được sử dụng khi thực hiện hội nghị truyền hình để các thiết bị bên ngoài như loa có thể được sử dụng với hội nghị truyền hình.';

  @override
  String xNumberOfDays(int x) {
    return '$x Ngày';
  }

  @override
  String get oneDay => '1 Ngày';

  @override
  String get oneHour => '1 giờ';

  @override
  String xNumberOfHours(int x) {
    return '$x Giờ';
  }

  @override
  String get today => 'Hôm nay';

  @override
  String get tomorrow => 'Ngày mai';

  @override
  String get refresh => 'Làm cho khỏe lại';

  @override
  String get noItemsToDisplay => 'Không có mục nào để hiển thị';

  @override
  String get week => 'Tuần';

  @override
  String get weeks => 'Tuần';

  @override
  String get month => 'Tháng';

  @override
  String get list => 'Danh sách';

  @override
  String get to => 'ĐẾN';

  @override
  String dateRange(DateTime startDate, DateTime endDate) {
    final intl.DateFormat startDateDateFormat = intl.DateFormat.yMd(localeName);
    final String startDateString = startDateDateFormat.format(startDate);
    final intl.DateFormat endDateDateFormat = intl.DateFormat.yMd(localeName);
    final String endDateString = endDateDateFormat.format(endDate);

    return '$startDateString đến $endDateString';
  }

  @override
  String date(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yMd(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String dateMonthYear(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.yM(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String dateWeekday(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.EEEE(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String dateHourOfDay(DateTime date) {
    final intl.DateFormat dateDateFormat = intl.DateFormat.j(localeName);
    final String dateString = dateDateFormat.format(date);

    return '$dateString';
  }

  @override
  String get standInTitle => 'Đứng Trong Tiêu Đề';

  @override
  String get appointmentChange => 'Thay đổi cuộc hẹn';

  @override
  String get requestAppointment => 'Yêu cầu cuộc hẹn';

  @override
  String get calendar => 'Lịch';

  @override
  String get notImplemented => 'Chưa thực hiện';

  @override
  String get requestSubmitted => 'Yêu cầu đã được gửi';

  @override
  String get submit => 'Nộp';

  @override
  String get requestToDelete => 'Yêu cầu xóa';

  @override
  String get submitRequestFailed => 'Gửi yêu cầu không thành công';

  @override
  String get noMessages => 'Bạn không có tin nhắn nào';

  @override
  String get translationMessage => 'Chúng tôi đã dịch cuộc trò chuyện này. Bạn có thể tắt bản dịch này bất kỳ lúc nào hoặc thay đổi ngôn ngữ ưa thích của bạn trong Cài đặt.';

  @override
  String get dontAskAgain => 'Đừng hỏi lại nữa';

  @override
  String get okay => 'Đồng ý';

  @override
  String get ok => 'Được rồi';

  @override
  String get startTime => 'Thời gian bắt đầu';

  @override
  String get endTime => 'Thời gian kết thúc';

  @override
  String get startDate => 'Ngày bắt đầu';

  @override
  String get endDate => 'Ngày kết thúc';

  @override
  String get biometrics => 'Sinh trắc học';

  @override
  String get privacyPolicy => 'Chính sách bảo mật';

  @override
  String get terms => 'Điều khoản và điều kiện';

  @override
  String get submitFeedback => 'Gửi phản hồi';

  @override
  String get profile => 'Hồ sơ';

  @override
  String get preferredLanguage => 'Ngôn ngữ ưa thích';

  @override
  String get mondayShort => 'T2';

  @override
  String get tuesdayShort => 'T3';

  @override
  String get wednesdayShort => 'T4';

  @override
  String get thursdayShort => 'T5';

  @override
  String get fridayShort => 'T6';

  @override
  String get saturdayShort => 'T7';

  @override
  String get sundayShort => 'CN';

  @override
  String get oneTime => 'Một lần';

  @override
  String get daily => 'Hằng ngày';

  @override
  String get weekly => 'Hàng tuần';

  @override
  String get debugTools => 'CÔNG CỤ GỠ LỖI';

  @override
  String get privacyPolicyTitle => 'Chính sách bảo mật';

  @override
  String get appointmentInPastValidation => 'Không thể yêu cầu cuộc hẹn trong quá khứ';

  @override
  String get fieldRequired => 'Yêu cầu';

  @override
  String get maxDate => 'Ngày tối đa';

  @override
  String pendingTasks(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Nhiệm vụ đang chờ xử lý ( $countString )',
      zero: 'Nhiệm vụ đang chờ xử lý',
    );
    return '$_temp0';
  }

  @override
  String xMinutesAgo(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countString phút trước',
      one: '1 phút trước',
      zero: 'Hiện nay',
    );
    return '$_temp0';
  }

  @override
  String xMinutesInFuture(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Trong $countString phút',
      one: 'Trong 1 phút',
      zero: 'Hiện nay',
    );
    return '$_temp0';
  }

  @override
  String xHoursAgo(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countString giờ trước',
      one: '1 giờ trước',
      zero: 'Hiện nay',
    );
    return '$_temp0';
  }

  @override
  String xHoursInFuture(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Trong $countString giờ',
      one: 'Trong 1 giờ',
      zero: 'Hiện nay',
    );
    return '$_temp0';
  }

  @override
  String xDaysAgo(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$countString ngày trước',
      one: '1 ngày trước',
      zero: 'Hiện nay',
    );
    return '$_temp0';
  }

  @override
  String xDaysInFuture(num count) {
    final intl.NumberFormat countNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String countString = countNumberFormat.format(count);

    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Trong $countString ngày',
      one: 'Ngày mai',
      zero: 'Hôm nay',
    );
    return '$_temp0';
  }

  @override
  String get join => 'Tham gia';

  @override
  String get reply => 'Hồi đáp';

  @override
  String get contactInformationLabel => 'Thông tin liên lạc';

  @override
  String get agencyPhoneNumberLabel => 'Số điện thoại của Cơ quan';

  @override
  String get agencyAddressLabel => 'Địa chỉ cơ quan';

  @override
  String get residenceInformationLabel => 'Thông tin nơi cư trú';

  @override
  String get reload => 'Tải lại';

  @override
  String get primary => 'Sơ đẳng';

  @override
  String get noTransportation => 'Không có phương tiện vận chuyển';

  @override
  String get addTransportation => 'Thêm Giao thông';

  @override
  String get filter => 'Lọc';

  @override
  String get all => 'Tất cả';

  @override
  String get transportation => 'Vận tải';

  @override
  String get unknown => 'Không rõ';

  @override
  String get genericError => 'Đã xảy ra lỗi';

  @override
  String get comments => 'Bình luận';

  @override
  String get state => 'tỉnh thành';

  @override
  String get color => 'Màu sắc';

  @override
  String get make => 'Làm';

  @override
  String get model => 'Người mẫu';

  @override
  String get modelYear => 'Năm mẫu';

  @override
  String get plate => 'Đĩa';

  @override
  String get errorModelYear => 'Vui lòng nhập năm hợp lệ gồm 4 chữ số.';

  @override
  String get clientOwned => 'Khách hàng sở hữu';

  @override
  String get primaryTransportation => 'Giao thông chính';

  @override
  String get tooLong => 'Quá dài';

  @override
  String get appointmentDetailsTitle => 'Chi tiết cuộc hẹn';

  @override
  String get website => 'Trang web';

  @override
  String get viewWebsite => 'Xem trang web';

  @override
  String get appointmentDateAndTime => 'Ngày & giờ hẹn';

  @override
  String get websiteGeneralError => 'Lỗi khi truy cập trang web';

  @override
  String get phoneGeneralError => 'Lỗi khi gọi điện';

  @override
  String get deleteTransporationConfirm => 'Bạn có chắc chắn muốn xóa phương tiện di chuyển của mình không?';

  @override
  String addressN(num n) {
    final intl.NumberFormat nNumberFormat = intl.NumberFormat.compact(
      locale: localeName,
      
    );
    final String nString = nNumberFormat.format(n);

    return 'Địa chỉ $nString';
  }

  @override
  String get timeZone => 'Múi giờ';

  @override
  String get city => 'Thành phố';

  @override
  String get invalidInputFormat => 'Định dạng không hợp lệ';

  @override
  String get noEndDate => 'Không có ngày kết thúc';

  @override
  String get appointmentRecurrsEvery => 'Cuộc hẹn được lặp lại mỗi';

  @override
  String get deleteEmploymentConfirm => 'Bạn có chắc chắn muốn xóa việc làm của mình không?';

  @override
  String get employment => 'Thuê người làm';

  @override
  String get noEmployment => 'Không có việc làm';

  @override
  String get addEmployment => 'Thêm việc làm';

  @override
  String get employer => 'Nhà tuyển dụng';

  @override
  String get fax => 'Fax';

  @override
  String get jobTitle => 'Chức danh công việc';

  @override
  String get supervisor => 'Người giám sát';

  @override
  String get hoursPerWeek => 'Giờ mỗi tuần';

  @override
  String get schedule => 'Lịch trình';

  @override
  String get initialHourlyPay => 'Lương giờ ban đầu';

  @override
  String get finalHourlyPay => 'Lương theo giờ cuối cùng';

  @override
  String get emailLabel => 'E-mail';

  @override
  String get primaryPhoneLabel => 'Số điện thoại chính';

  @override
  String get homePhoneLabel => 'Số nhà';

  @override
  String get mobilePhoneLabel => 'Số điện thoại di động';

  @override
  String get workPhoneLabel => 'số điện thoại công việc';

  @override
  String get workPhoneLabelSelection => 'Công việc';

  @override
  String get mobilePhoneSelectionLabel => 'Di động';

  @override
  String get primaryPhoneSelectionBlank => 'Số điện thoại chính không được để trống';

  @override
  String get zipCode => 'Mã bưu chính';

  @override
  String get county => 'Quận';

  @override
  String get personalVehicle => 'Xe cá nhân';

  @override
  String get publicTransportation => 'Giao thông công cộng';

  @override
  String get personalContact => 'Liên hệ cá nhân';

  @override
  String get taxi => 'Taxi';

  @override
  String get bicycle => 'Xe đạp';

  @override
  String get walk => 'Đi bộ';

  @override
  String get otherSpecify => 'Khác (nêu rõ)';

  @override
  String get notes => 'Ghi chú';

  @override
  String get personalContacts => 'Liên hệ cá nhân';

  @override
  String get additional => 'Thêm vào';

  @override
  String get response => 'Phản ứng';

  @override
  String get selfReportSubmitted => 'Đã nộp báo cáo!';

  @override
  String get selfReportError => 'Có lỗi khi gửi Báo cáo tự đánh giá!';

  @override
  String get serverError => 'Đã xảy ra lỗi nội bộ khi xử lý yêu cầu của bạn. Vui lòng liên hệ với cán bộ giám sát của bạn.';

  @override
  String get firstName => 'Tên đầu tiên';

  @override
  String get lastName => 'Họ';

  @override
  String get middleInitial => 'Chữ cái đầu ở giữa';

  @override
  String get citizenship => 'Quyền công dân';

  @override
  String get dateOfBirth => 'Ngày sinh';

  @override
  String get other => 'Khác';

  @override
  String get parent => 'Cha mẹ';

  @override
  String get spouse => 'Vợ chồng';

  @override
  String get exSpouse => 'Vợ/chồng cũ';

  @override
  String get child => 'Đứa trẻ';

  @override
  String get sibling => 'Anh em ruột';

  @override
  String get grandparent => 'Ông bà';

  @override
  String get extendedFamily => 'Gia đình mở rộng';

  @override
  String get friend => 'Bạn bè';

  @override
  String get attorney => 'Luật sư';

  @override
  String get inLaw => 'Trong luật pháp';

  @override
  String get guardian => 'Người bảo vệ';

  @override
  String get liveInFriend => 'Sống trong bạn bè';

  @override
  String get victim => 'Nạn nhân';

  @override
  String get childsFather => 'Cha của đứa trẻ';

  @override
  String get childAsContact => 'Trẻ em như là liên lạc';

  @override
  String get description => 'Sự miêu tả';

  @override
  String get livesWithClient => 'Sống với khách hàng';

  @override
  String get isProBono => 'Làm từ thiện';

  @override
  String get dependentOfClient => 'Phụ thuộc vào khách hàng';

  @override
  String get noPersonalContact => 'Không có liên hệ cá nhân';

  @override
  String get addPersonalContact => 'Thêm liên hệ cá nhân';

  @override
  String get deletePeronalContactConfirm => 'Bạn có chắc chắn muốn xóa liên hệ cá nhân của mình không?';

  @override
  String get deleteAppointmentConfirm => 'Bạn có chắc chắn muốn xóa cuộc hẹn của mình không?';

  @override
  String get failedToLoadCalendar => 'Không tải được mục lịch. Vui lòng thử lại hoặc liên hệ với cán bộ giám sát của bạn.';

  @override
  String get invalidDate => 'Ngày không hợp lệ';

  @override
  String get communityReferral => 'Giới thiệu cộng đồng';

  @override
  String get status => 'Trạng thái';

  @override
  String get appointmentTime => 'Thời gian hẹn';

  @override
  String get appointmentRecurrence => 'Cuộc hẹn tái diễn';

  @override
  String get appointmentNoChangesMade => 'Không có thay đổi nào được thực hiện đối với thông tin. Chỉnh sửa thông tin để gửi.';

  @override
  String get appointmentNoChangesMadeTitle => 'Không có thay đổi';

  @override
  String get noInternetConnection => 'Không có kết nối Internet';

  @override
  String get configurationUpdated => 'Cấu hình đã được cập nhật';

  @override
  String get pending => 'Chưa giải quyết';

  @override
  String get ongoing => 'Đang diễn ra';

  @override
  String get utilized => 'Đã sử dụng';

  @override
  String get notUtilized => 'Không sử dụng';

  @override
  String get instructions => 'Hướng dẫn';

  @override
  String get contactNotes => 'Ghi chú liên hệ';

  @override
  String get none => 'Không có';

  @override
  String get resources => 'Tài nguyên';

  @override
  String get documents => 'Tài liệu';

  @override
  String get documentsSubmited => 'Đã nộp tài liệu!';

  @override
  String get beginSetup => 'Bắt đầu thiết lập';

  @override
  String get mdmAppPermissionNotGranted => 'Ứng dụng MDM Plugin thiếu quyền Điện thoại. Vui lòng kiểm tra quyền Ứng dụng MDM Plugin hoặc cấu hình Samsung Knox.';

  @override
  String get phoneNotAssigned => 'Điện thoại chưa được chỉ định';

  @override
  String get missingMdmPhoneNumber => 'Không thể xác định số điện thoại. Vui lòng đảm bảo đã lắp thẻ SIM và thử lại.';

  @override
  String get hours => 'Giờ';

  @override
  String get name => 'Tên';

  @override
  String get resource => 'Tài nguyên';

  @override
  String get businessHours => 'Giờ làm việc';

  @override
  String get badLink => 'Liên kết được cung cấp không thể giải quyết được';

  @override
  String get endMeeting => 'KẾT THÚC CUỘC HỌP';

  @override
  String get videoLoadingMessage => 'Đang kết nối với phòng...';

  @override
  String get emptyVideoRoomMessage => 'Đang chờ người tham gia khác kết nối vào phòng...';

  @override
  String get nearByDevices => 'Thiết bị gần đó';

  @override
  String get camera => 'Máy ảnh';

  @override
  String get microphone => 'Micrô';

  @override
  String get location => 'Vị trí';

  @override
  String get permissionDialogTitle => 'Cần có quyền để tiếp tục ';

  @override
  String get noAvailableVCErrorMessage => 'Không có cuộc gọi nào khả dụng ngay bây giờ';

  @override
  String get sessionExpired => 'Phiên đã hết thời gian';

  @override
  String get accept => 'Chấp nhận';

  @override
  String get retake => 'Làm lại';

  @override
  String get usePhoto => 'Sử dụng ảnh';

  @override
  String upload(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Tải lên ( $count )',
    );
    return '$_temp0';
  }

  @override
  String get vcDialogTitle => 'Cuộc gọi hội nghị video đến';

  @override
  String get enrollment => 'Đăng ký';

  @override
  String get guidelines => 'Hướng dẫn';

  @override
  String get fullFrameFace => 'Khuôn mặt toàn khung';

  @override
  String get simpleBackground => 'Nền đơn giản';

  @override
  String get neutralExpression => 'Biểu hiện trung tính';

  @override
  String get levelCamera => 'Camera căn chỉnh mức';

  @override
  String get optimalDistance => 'Khoảng cách tối ưu';

  @override
  String get goodLighting => 'Ánh sáng tốt';

  @override
  String get doNot => 'Không được ';

  @override
  String get noSmile => 'mỉm cười, quay đầu hoặc nhắm mắt';

  @override
  String get noOtherPeople => 'bao gồm nhiều người hoặc động vật';

  @override
  String get extendArm => 'duỗi thẳng hoặc duỗi thẳng cánh tay của bạn';

  @override
  String get noFunnyFaces => 'sử dụng môi vịt hoặc góc lạ';

  @override
  String get wearSunglasses => 'đeo kính râm';

  @override
  String get noCamerasAvaliable => 'Có vẻ như không có máy ảnh nào khả dụng. Vui lòng đóng ứng dụng và thử lại.';

  @override
  String get callHistory => 'Lịch sử cuộc gọi';

  @override
  String get callHistoryLoadingMessage => 'Đang tải lịch sử cuộc gọi của bạn...';

  @override
  String get settingPin => 'Thiết lập mã PIN của bạn';

  @override
  String get enterYourPin => 'Nhập mã PIN của bạn';

  @override
  String get reEnterYourPin => 'Xác nhận mã PIN của bạn';

  @override
  String get pinsDoNotMatch => 'Mã PIN không khớp. Hãy thử lại.';

  @override
  String get pinSetupComplete => 'Thiết lập mã PIN hoàn tất';

  @override
  String get incorrectPin => 'Mã PIN không đúng';

  @override
  String get resetPin => 'Đặt lại mã PIN';

  @override
  String get pinResetQuestion => 'Bạn có muốn đặt lại mã PIN không?';

  @override
  String get tryingToResetPin => 'Đang cố gắng thiết lập lại mã PIN';

  @override
  String get pinResetSuccess => 'Đặt lại mã PIN thành công';

  @override
  String get pinResetRequestFailed => 'Đặt lại mã PIN không thành công. Vui lòng thử lại.';

  @override
  String get forgotPin => 'Quên mã PIN';

  @override
  String get waitingForVerification => 'Đang chờ xác minh';

  @override
  String get waitingForVerificationTrailing => 'Đang chờ xác minh...';

  @override
  String get enrollmentComplete => 'Đã nộp đơn đăng ký!';

  @override
  String get submittingEnrollment => 'Nộp đơn ghi danh';

  @override
  String get enrollmentError => 'Đã xảy ra lỗi khi đăng ký. Vui lòng kiểm tra hướng dẫn và thử lại.';

  @override
  String get enrollmentDescription => 'Đăng ký yêu cầu một loạt ảnh của bạn. Nhấn Tiếp tục để mở chế độ xem camera. Căn giữa khuôn mặt của bạn vào đường viền và đợi camera chụp ảnh.';

  @override
  String get callHistoryBannerError => 'Không tải được lịch sử cuộc gọi của bạn. Vui lòng thử lại hoặc liên hệ với nhân viên giám sát của bạn.';

  @override
  String get checkIn => 'Đăng ký vào';

  @override
  String get uploadLogs => 'Tải lên nhật ký';

  @override
  String get phoneCallDialogTitle => 'Cuộc gọi đến';

  @override
  String get callInProgress => 'Cuộc gọi đang diễn ra';

  @override
  String get openPhoneAccountSettings => 'Vui lòng mở đăng ký BI SmartLink trong cài đặt tài khoản điện thoại của bạn,';

  @override
  String get dismiss => 'Miễn nhiệm';

  @override
  String get report => 'Báo cáo';

  @override
  String get submittingCheckIn => 'Nộp đơn đăng ký';

  @override
  String get sessionExpiredMessage => 'Phiên của bạn đã hết thời gian. Vui lòng đăng nhập lại để tiếp tục sử dụng ứng dụng.';

  @override
  String get updateRequired => 'Yêu cầu cập nhật';

  @override
  String get installUpdate => 'Cài đặt Cập nhật';

  @override
  String get callerHungUpMessage => 'Người gọi đã cúp máy.';

  @override
  String get emptyCallHistoryMessage => 'Không có cuộc gọi gần đây';

  @override
  String get voipCallPermissionsDialogMessage => 'Để đảm bảo đầy đủ chức năng, vui lòng bật quyền gọi VoIP trong cài đặt của bạn.';

  @override
  String noCheckInsAllowed(String futureDate) {
    return 'Không được phép check-in cho đến $futureDate trừ khi bạn nhận được thông báo yêu cầu check-in.';
  }

  @override
  String get biometricCheckInDue => 'Đến hạn kiểm tra sinh trắc học';

  @override
  String get selfReportCheckInDue => 'Tự kiểm tra báo cáo đến hạn';

  @override
  String get newCalendarItem => 'Mục Lịch Mới';

  @override
  String get newDocument => 'Tài liệu mới';

  @override
  String get newVideoConference => 'Hội nghị truyền hình mới';

  @override
  String get calendarChangeRequestSubmitted => 'Đã gửi yêu cầu thay đổi!';

  @override
  String get requestNotificationResponse => 'Bạn sẽ nhận được thông báo khi nhân viên phản hồi.';

  @override
  String get permissionRequired => 'Cần có sự cho phép';

  @override
  String get permissionRequiredToUseThisPlugin => 'Cần có quyền để sử dụng plugin này.';

  @override
  String get street => 'Đường phố';

  @override
  String withColonSuffixAndSpaces(String value) {
    return '$value : ';
  }

  @override
  String get warning => 'Cảnh báo';

  @override
  String get enableLocation => 'Dịch vụ định vị thiết bị hiện đang bị vô hiệu hóa. Vui lòng bật dịch vụ định vị cho ứng dụng. ';

  @override
  String get streetName => 'Tên Đường Phố';

  @override
  String get genericChangeRequestSubmitted => 'Đã gửi yêu cầu thay đổi!';

  @override
  String withSpaceDashShapce(String value) {
    return '$value - ';
  }

  @override
  String prefixWithProfile(String value) {
    return 'Hồ sơ - $value';
  }

  @override
  String get vcHungUpMessage => 'Hiện tại không thể gọi điện.';

  @override
  String get profileNoContacts => 'Không có liên hệ';

  @override
  String get relationship => 'Mối quan hệ';

  @override
  String get checkInDescription => 'Vui lòng căn chỉnh khuôn mặt của bạn theo đường viền trong khi giữ điện thoại ngang tầm mắt. Ảnh của bạn sẽ được chụp tự động.';

  @override
  String get checkInComplete => 'Đã gửi yêu cầu đăng ký!';

  @override
  String get takePhoto => 'Chụp ảnh';

  @override
  String get referral => 'Giới thiệu';

  @override
  String get restartPhone => 'Vui lòng khởi động lại điện thoại';

  @override
  String get gettingStarted => 'Bắt đầu';

  @override
  String get newFeatures => 'Thiết kế và tính năng mới của SmartLINK';

  @override
  String get done => 'Xong';

  @override
  String get next => 'Kế tiếp';

  @override
  String get previous => 'Trước';

  @override
  String get todoTasksTitle => 'Nhiệm vụ cần làm';

  @override
  String get hamburgerMenu => 'Các mục trong thực đơn Hamburger';

  @override
  String get myInfo => 'Hồ sơ thay thế Thông tin của tôi';

  @override
  String get dashboardTabs => 'Trang chủ';

  @override
  String get accessCheckIn => 'Truy cập Check-In';

  @override
  String get close => 'Đóng';

  @override
  String get enrollmentCompletePNTitle => 'Đăng ký hoàn tất';

  @override
  String get fixedBiometricPNTitle => 'Đăng ký sinh trắc học';

  @override
  String get bluetoothDisabled => 'Bluetooth đã tắt';

  @override
  String get enableInSettings => 'Bật trong Cài đặt';

  @override
  String get locationServiceDisabled => 'Dịch vụ định vị bị vô hiệu hóa';

  @override
  String get enableLocationTodoItemTitle => 'Dịch vụ định vị bị vô hiệu hóa';

  @override
  String get appName => 'BI SmartLINK';

  @override
  String get trackingRunningInBackground => 'BI SmartLINK sẽ theo dõi vị trí của bạn ở chế độ nền để đảm bảo tuân thủ chương trình.';

  @override
  String get centerFace => 'Đặt khuôn mặt của bạn vào giữa đường viền';

  @override
  String get blinkAndTakeBreath => 'Nháy mắt và hít thở thật sâu';

  @override
  String get waitUpto15Seconds => 'Chờ tối đa 15 giây';

  @override
  String get autoCapture => 'Hình ảnh của bạn sẽ được chụp tự động';

  @override
  String get takeOff => 'Cởi kính, mũ hoặc bất cứ thứ gì che mặt bạn ra';

  @override
  String get useNeutralExpression => 'Sử dụng một biểu thức trung lập';

  @override
  String get wellLit => 'Hãy đảm bảo khuôn mặt của bạn được chiếu sáng tốt';

  @override
  String get joiningVideoConferenceDialogTitle => 'Tham gia...';

  @override
  String get preferences => 'Sở thích';

  @override
  String get legal => 'Hợp pháp';

  @override
  String get smartband => 'Vòng đeo tay thông minh';

  @override
  String get firmwareVersion => 'Phiên bản phần mềm';

  @override
  String get smartbandId => 'Mã số SmartBand';

  @override
  String get regulatoryLabels => 'Nhãn quy định';

  @override
  String get rfExposure => 'Tiếp xúc RF';

  @override
  String get rfExposureTitle => 'Tỷ lệ hấp thụ riêng (SAR)';

  @override
  String get rfExposureParagraph1 => 'Thiết bị này đáp ứng các hướng dẫn quốc tế về tiếp xúc với năng lượng RF. Thiết bị được thiết kế để không vượt quá giới hạn tiếp xúc với năng lượng RF theo khuyến nghị của các hướng dẫn quốc tế. Các hướng dẫn được phát triển bởi một tổ chức khoa học độc lập (ICNIRP) và bao gồm một biên độ an toàn đáng kể được thiết kế để đảm bảo an toàn cho tất cả mọi người bất kể tuổi tác và sức khỏe.';

  @override
  String get rfExposureParagraph2 => 'Giới hạn SAR cho thiết bị di động là 1,6 W/Kg với khoảng cách tách biệt như sau:';

  @override
  String get rfExposureHead => 'Đầu – 0 mm';

  @override
  String get rfExposureBody => 'Thân – 10 mm';

  @override
  String get designedTested => 'Được thiết kế, thử nghiệm và lắp ráp tại Hoa Kỳ';

  @override
  String get smartbandColor => 'Màu sắc: Đen/Xanh';

  @override
  String get notAssigned => 'Chưa được giao';

  @override
  String appSettingsVersionText(String version) {
    return 'Phiên bản $version';
  }

  @override
  String fccId(String fccId) {
    return 'Chứa ID FCC: $fccId';
  }

  @override
  String icId(String isedId) {
    return 'Chứa ID ISED: $isedId';
  }

  @override
  String biRegulatoryModel(String model) {
    return 'MÔ HÌNH BI: $model';
  }

  @override
  String get goToSettings => 'Vào Cài đặt';

  @override
  String get enableBluetooth => 'Bật Bluetooth để kết nối với thiết bị SmartBand của bạn.';

  @override
  String get bluetooth => 'Bluetooth';

  @override
  String get locationAlwaysPermissionRationale => 'Ứng dụng này cần quyền truy cập vào vị trí của bạn mọi lúc để tuân thủ các yêu cầu của chương trình. Dữ liệu này được truyền và lưu trữ, nhưng không được chia sẻ với bên thứ ba. Bạn sẽ được hướng dẫn bật quyền \"Cho phép mọi lúc\".';

  @override
  String get checkingForFotaMessage => 'Đang kiểm tra SmartBand FOTA có sẵn...';

  @override
  String checkingForSpecificFotaMessage(String version) {
    return 'Đang kiểm tra phiên bản SmartBand FOTA $version ...';
  }

  @override
  String get smartBandUpdateComplete => 'Hoàn tất cập nhật SmartBand!';

  @override
  String get smartBandUpdateFailed => 'Không cập nhật được chương trình cơ sở SmartBand.';

  @override
  String get smartBandUpdateInvalidFirmwareFile => 'Tệp FOTA không hợp lệ. Hãy đảm bảo phiên bản tồn tại và chính xác trước khi thử lại.';

  @override
  String get checkForSmartBandFota => 'Kiểm tra SmartBand FOTA';

  @override
  String get smartBandFotaVersionToDownload => 'Phiên bản SmartBand FOTA để tải xuống';

  @override
  String get notificationPermissionRationale => 'Ứng dụng này sẽ gửi cho bạn các bản cập nhật để giúp bạn tuân thủ các yêu cầu của chương trình. Vui lòng cấp quyền trong hộp thoại tiếp theo.';

  @override
  String get genericPermissionRationale => 'Ứng dụng này cần thêm quyền để giúp copmly đáp ứng các yêu cầu của chương trình. Vui lòng cấp quyền trong hộp thoại tiếp theo.';
}

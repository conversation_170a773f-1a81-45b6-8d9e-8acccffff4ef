//these imports will be needed when we actually override this class properly
import 'package:bi_embark_l10n/l10n/so_date_time.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;
import 'package:intl/number_symbols_data.dart' as number_symbol_data;

class _SoMaterialLocalizationsDelegate
    extends LocalizationsDelegate<MaterialLocalizations> {
  const _SoMaterialLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => locale.languageCode == 'so';

  @override
  Future<MaterialLocalizations> load(Locale locale) async {
    final String localeName = intl.Intl.canonicalizedLocale(locale.toString());

    SomaliDateTime.initialize();

    // Apply english number formats for the so language.
    if (!number_symbol_data.compactNumberSymbols.containsKey('so')) {
      // add so symbols which is just english but tied to the so locale
      number_symbol_data.compactNumberSymbols['so'] =
          number_symbol_data.compactNumberSymbols['en']!;

      number_symbol_data.numberFormatSymbols['so'] =
          number_symbol_data.numberFormatSymbols['en']!;
    }

    final somaliLocalization = SoMaterialLocalizations(
      fullYearFormat: intl.DateFormat('y', localeName),
      shortDateFormat: intl.DateFormat('yMMMd', localeName),
      compactDateFormat: intl.DateFormat('dd/MM/yyyy', localeName), //compact
      mediumDateFormat: intl.DateFormat('EEE, MMM d', localeName),
      longDateFormat: intl.DateFormat('EEEE, MMMM d, y', localeName),
      yearMonthFormat: intl.DateFormat('MMMM y', localeName),
      shortMonthDayFormat: intl.DateFormat('MMM d', localeName),
      // The `intl` library's NumberFormat class is generated from CLDR data
      // (see https://github.com/dart-lang/intl/blob/master/lib/number_symbols_data.dart).
      // Unfortunately, there is no way to use a locale that isn't defined in
      // this map and the only way to work around this is to use a listed
      // locale's NumberFormat symbols. So, here we use the number formats
      // for 'en_US' instead.
      decimalFormat: intl.NumberFormat('#,##0.###', 'en_US'),
      twoDigitZeroPaddedFormat: intl.NumberFormat('00', 'en_US'),
    );

    return SynchronousFuture<MaterialLocalizations>(somaliLocalization);
  }

  @override
  bool shouldReload(
    covariant LocalizationsDelegate<MaterialLocalizations> old,
  ) {
    return false;
  }
}

class SoMaterialLocalizations extends GlobalMaterialLocalizations {
  const SoMaterialLocalizations({
    super.localeName = 'so',
    required super.fullYearFormat,
    required super.compactDateFormat,
    required super.shortDateFormat,
    required super.mediumDateFormat,
    required super.longDateFormat,
    required super.yearMonthFormat,
    required super.shortMonthDayFormat,
    required super.decimalFormat,
    required super.twoDigitZeroPaddedFormat,
  });

  static const LocalizationsDelegate<MaterialLocalizations> delegate =
      _SoMaterialLocalizationsDelegate();

  @override
  String get aboutListTileTitleRaw => r'Ku saabsan $applicationName';

  @override
  String get alertDialogLabel =>
      const DefaultMaterialLocalizations().alertDialogLabel;

  @override
  String get anteMeridiemAbbreviation =>
      const DefaultMaterialLocalizations().anteMeridiemAbbreviation;

  @override
  String get backButtonTooltip =>
      const DefaultMaterialLocalizations().backButtonTooltip;

  @override
  String get calendarModeButtonLabel =>
      const DefaultMaterialLocalizations().calendarModeButtonLabel;

  @override
  String get cancelButtonLabel => 'BAAQ';

  @override
  String get closeButtonLabel =>
      const DefaultMaterialLocalizations().closeButtonLabel;

  @override
  String get closeButtonTooltip =>
      const DefaultMaterialLocalizations().closeButtonTooltip;

  @override
  String get collapsedIconTapHint =>
      const DefaultMaterialLocalizations().collapsedIconTapHint;

  @override
  String get continueButtonLabel =>
      const DefaultMaterialLocalizations().continueButtonLabel;

  @override
  String get copyButtonLabel =>
      const DefaultMaterialLocalizations().copyButtonLabel;

  @override
  String get cutButtonLabel =>
      const DefaultMaterialLocalizations().cutButtonLabel;

  @override
  String get dateHelpText => const DefaultMaterialLocalizations().dateHelpText;

  @override
  String get dateInputLabel => 'GELI TAARIIKHDA'; //Enter date

  @override
  String get dateOutOfRangeLabel => 'Banaanka'; // 'Out of range.';

  @override
  String get datePickerHelpText => 'DOORASHADA TAARIIKHDA'; //Select date

  @override
  String get dateRangeEndDateSemanticLabelRaw =>
      r'Taariikhda dhamaadka $fullDate';

  @override
  String get dateRangeEndLabel =>
      const DefaultMaterialLocalizations().dateRangeEndLabel;

  @override
  String get dateRangePickerHelpText =>
      const DefaultMaterialLocalizations().dateRangePickerHelpText;

  @override
  String get dateRangeStartDateSemanticLabelRaw =>
      r'Taariikhda bilawga $fullDate';

  @override
  String get dateRangeStartLabel =>
      const DefaultMaterialLocalizations().dateRangeStartLabel;

  @override
  String get dateSeparator =>
      const DefaultMaterialLocalizations().dateSeparator;

  @override
  String get deleteButtonTooltip =>
      const DefaultMaterialLocalizations().deleteButtonTooltip;

  @override
  String get dialModeButtonLabel =>
      const DefaultMaterialLocalizations().dialModeButtonLabel;

  @override
  String get dialogLabel => const DefaultMaterialLocalizations().dialogLabel;

  @override
  String get drawerLabel => const DefaultMaterialLocalizations().drawerLabel;

  @override
  String get expandedIconTapHint =>
      const DefaultMaterialLocalizations().expandedIconTapHint;

  @override
  String get firstPageTooltip =>
      const DefaultMaterialLocalizations().firstPageTooltip;

  @override
  String get hideAccountsLabel =>
      const DefaultMaterialLocalizations().hideAccountsLabel;

  @override
  String get inputDateModeButtonLabel =>
      const DefaultMaterialLocalizations().inputDateModeButtonLabel;

  @override
  String get inputTimeModeButtonLabel =>
      const DefaultMaterialLocalizations().inputTimeModeButtonLabel;

  @override
  String get invalidDateFormatLabel => 'Qaab aan sax ahayn'; //Invalid format.';

  @override
  String get invalidDateRangeLabel =>
      const DefaultMaterialLocalizations().invalidDateRangeLabel;

  @override
  String get invalidTimeLabel =>
      'Fadlan geli wakhti sax ah'; // 'Enter a valid time';

  @override
  String get lastPageTooltip =>
      const DefaultMaterialLocalizations().lastPageTooltip;

  @override
  String get licensesPackageDetailTextOther => r'$licenseCount shatiyada';

  @override
  String get licensesPageTitle =>
      const DefaultMaterialLocalizations().licensesPageTitle;

  @override
  String get modalBarrierDismissLabel =>
      const DefaultMaterialLocalizations().modalBarrierDismissLabel;

  @override
  String get moreButtonTooltip =>
      const DefaultMaterialLocalizations().moreButtonTooltip;

  @override
  String get nextMonthTooltip =>
      const DefaultMaterialLocalizations().nextMonthTooltip;

  @override
  String get nextPageTooltip =>
      const DefaultMaterialLocalizations().nextPageTooltip;

  @override
  String get okButtonLabel => 'OK';

  @override
  String get openAppDrawerTooltip =>
      const DefaultMaterialLocalizations().openAppDrawerTooltip;

  @override
  String get pageRowsInfoTitleApproximateRaw =>
      r'$firstRow–$lastRow ku saabsan $rowCount';

  @override
  String get pageRowsInfoTitleRaw => r'$firstRow–$lastRow ee $rowCount';

  @override
  String get pasteButtonLabel =>
      const DefaultMaterialLocalizations().pasteButtonLabel;

  @override
  String get popupMenuLabel =>
      const DefaultMaterialLocalizations().popupMenuLabel;

  @override
  String get postMeridiemAbbreviation =>
      const DefaultMaterialLocalizations().postMeridiemAbbreviation;

  @override
  String get previousMonthTooltip =>
      const DefaultMaterialLocalizations().previousMonthTooltip;

  @override
  String get previousPageTooltip =>
      const DefaultMaterialLocalizations().previousPageTooltip;

  @override
  String get refreshIndicatorSemanticLabel =>
      const DefaultMaterialLocalizations().refreshIndicatorSemanticLabel;

  @override
  String get remainingTextFieldCharacterCountOther =>
      r'$remainingCount jilayaasha haray';

  @override
  String get reorderItemDown =>
      const DefaultMaterialLocalizations().reorderItemDown;

  @override
  String get reorderItemLeft =>
      const DefaultMaterialLocalizations().reorderItemLeft;

  @override
  String get reorderItemRight =>
      const DefaultMaterialLocalizations().reorderItemRight;

  @override
  String get reorderItemToEnd =>
      const DefaultMaterialLocalizations().reorderItemToEnd;

  @override
  String get reorderItemToStart =>
      const DefaultMaterialLocalizations().reorderItemToStart;

  @override
  String get reorderItemUp =>
      const DefaultMaterialLocalizations().reorderItemUp;

  @override
  String get rowsPerPageTitle =>
      const DefaultMaterialLocalizations().rowsPerPageTitle;

  @override
  String get saveButtonLabel =>
      const DefaultMaterialLocalizations().saveButtonLabel;

  @override
  ScriptCategory get scriptCategory =>
      const DefaultMaterialLocalizations().scriptCategory;

  @override
  String get searchFieldLabel =>
      const DefaultMaterialLocalizations().searchFieldLabel;

  @override
  String get selectAllButtonLabel =>
      const DefaultMaterialLocalizations().selectAllButtonLabel;

  @override
  String get selectYearSemanticsLabel =>
      const DefaultMaterialLocalizations().selectYearSemanticsLabel;

  @override
  String get selectedRowCountTitleOther =>
      r'$selectedRowCount alaabta la doortay';

  @override
  String get showAccountsLabel =>
      const DefaultMaterialLocalizations().showAccountsLabel;

  @override
  String get showMenuTooltip =>
      const DefaultMaterialLocalizations().showMenuTooltip;

  @override
  String get signedInLabel =>
      const DefaultMaterialLocalizations().signedInLabel;

  @override
  String get tabLabelRaw => r'Tab $tabIndex ee $tabCount';

  @override
  TimeOfDayFormat get timeOfDayFormatRaw => TimeOfDayFormat.h_colon_mm_space_a;

  @override
  String get timePickerDialHelpText => 'WAQTIGA DOORAN'; //Select time

  @override
  String get timePickerHourLabel => 'GOORMA'; //Hour

  @override
  String get timePickerHourModeAnnouncement =>
      const DefaultMaterialLocalizations().timePickerHourModeAnnouncement;

  @override
  String get timePickerInputHelpText => 'WAQTIGA GELI'; //Enter time

  @override
  String get timePickerMinuteLabel => 'DAQIIQADII'; //Minute

  @override
  String get timePickerMinuteModeAnnouncement =>
      const DefaultMaterialLocalizations().timePickerMinuteModeAnnouncement;

  @override
  String get unspecifiedDate =>
      const DefaultMaterialLocalizations().unspecifiedDate;

  @override
  String get unspecifiedDateRange =>
      const DefaultMaterialLocalizations().unspecifiedDateRange;

  @override
  String get viewLicensesButtonLabel =>
      const DefaultMaterialLocalizations().viewLicensesButtonLabel;

  @override
  String get keyboardKeyAlt =>
      const DefaultMaterialLocalizations().keyboardKeyAlt;

  @override
  String get keyboardKeyAltGraph =>
      const DefaultMaterialLocalizations().keyboardKeyAltGraph;

  @override
  String get keyboardKeyBackspace =>
      const DefaultMaterialLocalizations().keyboardKeyBackspace;

  @override
  String get keyboardKeyCapsLock =>
      const DefaultMaterialLocalizations().keyboardKeyCapsLock;

  @override
  String get keyboardKeyChannelDown =>
      const DefaultMaterialLocalizations().keyboardKeyChannelDown;

  @override
  String get keyboardKeyChannelUp =>
      const DefaultMaterialLocalizations().keyboardKeyChannelUp;

  @override
  String get keyboardKeyControl =>
      const DefaultMaterialLocalizations().keyboardKeyControl;

  @override
  String get keyboardKeyDelete =>
      const DefaultMaterialLocalizations().keyboardKeyDelete;

  @override
  String get keyboardKeyEject =>
      const DefaultMaterialLocalizations().keyboardKeyEject;

  @override
  String get keyboardKeyEnd =>
      const DefaultMaterialLocalizations().keyboardKeyEnd;

  @override
  String get keyboardKeyEscape =>
      const DefaultMaterialLocalizations().keyboardKeyEscape;

  @override
  String get keyboardKeyFn =>
      const DefaultMaterialLocalizations().keyboardKeyFn;

  @override
  String get keyboardKeyHome =>
      const DefaultMaterialLocalizations().keyboardKeyHome;

  @override
  String get keyboardKeyInsert =>
      const DefaultMaterialLocalizations().keyboardKeyInsert;

  @override
  String get keyboardKeyMeta =>
      const DefaultMaterialLocalizations().keyboardKeyMeta;

  @override
  String get keyboardKeyMetaMacOs =>
      const DefaultMaterialLocalizations().keyboardKeyMetaMacOs;

  @override
  String get keyboardKeyMetaWindows =>
      const DefaultMaterialLocalizations().keyboardKeyMetaWindows;

  @override
  String get keyboardKeyNumLock =>
      const DefaultMaterialLocalizations().keyboardKeyNumLock;

  @override
  String get keyboardKeyNumpad0 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad0;

  @override
  String get keyboardKeyNumpad1 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad1;

  @override
  String get keyboardKeyNumpad2 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad2;

  @override
  String get keyboardKeyNumpad3 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad3;

  @override
  String get keyboardKeyNumpad4 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad4;

  @override
  String get keyboardKeyNumpad5 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad5;

  @override
  String get keyboardKeyNumpad6 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad6;

  @override
  String get keyboardKeyNumpad7 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad7;

  @override
  String get keyboardKeyNumpad8 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad8;

  @override
  String get keyboardKeyNumpad9 =>
      const DefaultMaterialLocalizations().keyboardKeyNumpad9;

  @override
  String get keyboardKeyNumpadAdd =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadAdd;

  @override
  String get keyboardKeyNumpadComma =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadComma;

  @override
  String get keyboardKeyNumpadDecimal =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadDecimal;

  @override
  String get keyboardKeyNumpadDivide =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadDivide;

  @override
  String get keyboardKeyNumpadEnter =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadEnter;

  @override
  String get keyboardKeyNumpadEqual =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadEqual;

  @override
  String get keyboardKeyNumpadMultiply =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadMultiply;

  @override
  String get keyboardKeyNumpadParenLeft =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadParenLeft;

  @override
  String get keyboardKeyNumpadParenRight =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadParenRight;

  @override
  String get keyboardKeyNumpadSubtract =>
      const DefaultMaterialLocalizations().keyboardKeyNumpadSubtract;

  @override
  String get keyboardKeyPageDown =>
      const DefaultMaterialLocalizations().keyboardKeyPageDown;

  @override
  String get keyboardKeyPageUp =>
      const DefaultMaterialLocalizations().keyboardKeyPageUp;

  @override
  String get keyboardKeyPower =>
      const DefaultMaterialLocalizations().keyboardKeyPower;

  @override
  String get keyboardKeyPowerOff =>
      const DefaultMaterialLocalizations().keyboardKeyPowerOff;

  @override
  String get keyboardKeyPrintScreen =>
      const DefaultMaterialLocalizations().keyboardKeyPrintScreen;

  @override
  String get keyboardKeyScrollLock =>
      const DefaultMaterialLocalizations().keyboardKeyScrollLock;

  @override
  String get keyboardKeySelect =>
      const DefaultMaterialLocalizations().keyboardKeySelect;

  @override
  String get keyboardKeySpace =>
      const DefaultMaterialLocalizations().keyboardKeySpace;

  @override
  String get menuBarMenuLabel =>
      const DefaultMaterialLocalizations().menuBarMenuLabel;

  @override
  String get bottomSheetLabel =>
      const DefaultMaterialLocalizations().bottomSheetLabel;

  @override
  String get currentDateLabel =>
      const DefaultMaterialLocalizations().currentDateLabel;

  @override
  String get keyboardKeyShift =>
      const DefaultMaterialLocalizations().keyboardKeyShift;

  @override
  String get scrimLabel => const DefaultMaterialLocalizations().scrimLabel;

  @override
  String get scrimOnTapHintRaw => r'Xir $modalRouteContentName';

  @override
  String get collapsedHint =>
      const DefaultMaterialLocalizations().collapsedHint;

  @override
  String get expandedHint => const DefaultMaterialLocalizations().expandedHint;

  @override
  String get expansionTileCollapsedHint =>
      const DefaultMaterialLocalizations().expansionTileCollapsedHint;

  @override
  String get expansionTileCollapsedTapHint =>
      const DefaultMaterialLocalizations().expansionTileCollapsedTapHint;

  @override
  String get expansionTileExpandedHint =>
      const DefaultMaterialLocalizations().expansionTileExpandedHint;

  @override
  String get expansionTileExpandedTapHint =>
      const DefaultMaterialLocalizations().expansionTileExpandedTapHint;

  @override
  String get scanTextButtonLabel =>
      const DefaultMaterialLocalizations().scanTextButtonLabel;

  @override
  String get lookUpButtonLabel => 'Kor u eeg.';

  @override
  String get menuDismissLabel => 'Dib u dhig liiska';

  @override
  String get searchWebButtonLabel => 'Raadinta internetka';

  @override
  String get shareButtonLabel => 'La wadaag...';

  @override
  String get clearButtonTooltip => 'Cad';

  @override
  String get selectedDateLabel => 'Dooro';
}

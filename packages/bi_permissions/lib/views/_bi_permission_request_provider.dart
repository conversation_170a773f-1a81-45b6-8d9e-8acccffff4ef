import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:bi_permissions/views/_bi_permission_request_handler.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

class BiPermissionRequestProvider extends StatelessWidget {
  final String Function(BuildContext context, BiPermission permission)
      buildRationaleTitle;
  final String Function(BuildContext context, BiPermission permission)
      buildRationale;

  final String continueButtonText;
  final String cancelButtonText;

  final GlobalKey? navigatorKey;
  final Widget? child;

  final BiPermissionRequestRepository Function(BuildContext) create;

  const BiPermissionRequestProvider({
    super.key,
    required this.buildRationaleTitle,
    required this.buildRationale,
    required this.continueButtonText,
    required this.cancelButtonText,
    this.navigatorKey,
    required this.create,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Provider(
      create: create,
      child: BiPermissionRequestHandler(
        buildRationale: buildRationale,
        buildRationaleTitle: buildRationaleTitle,
        continueButtonText: continueButtonText,
        cancelButtonText: cancelButtonText,
        child: child,
      ),
    );
  }
}

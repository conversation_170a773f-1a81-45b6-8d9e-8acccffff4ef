import 'package:bi_flutter_widgets/bi_widgets.dart';
import 'package:bi_permissions/cubit/_bi_permission_request_handler_cubit.dart';
import 'package:bi_permissions/cubit/_bi_permission_request_handler_state.dart';
import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';

class BiPermissionRequestHandler extends StatelessWidget {
  final String Function(BuildContext context, BiPermission permission)
      buildRationaleTitle;
  final String Function(BuildContext context, BiPermission permission)
      buildRationale;

  final String continueButtonText;
  final String cancelButtonText;

  final Widget? child;

  const BiPermissionRequestHandler({
    super.key,
    required this.buildRationale,
    required this.buildRationaleTitle,
    required this.continueButtonText,
    required this.cancelButtonText,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => BiPermissionRequestHandlerCubit(
        Provider.of<BiPermissionRequestRepository>(context),
      ),
      child: _LifecycleListener(
        child: _PermissionRationaleListener(
          buildRationale: buildRationale,
          buildRationaleTitle: buildRationaleTitle,
          continueButtonText: continueButtonText,
          cancelButtonText: cancelButtonText,
          child: child,
        ),
      ),
    );
  }
}

class _PermissionRationaleListener extends StatelessWidget {
  final String Function(BuildContext context, BiPermission permission)
      buildRationaleTitle;
  final String Function(BuildContext context, BiPermission permission)
      buildRationale;
  final String continueButtonText;
  final String cancelButtonText;

  final Widget? child;

  const _PermissionRationaleListener({
    required this.buildRationale,
    required this.buildRationaleTitle,
    this.child,
    required this.continueButtonText,
    required this.cancelButtonText,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<BiPermissionRequestHandlerCubit,
        BiPermissionRequestHandlerState>(
      listenWhen: (prev, curr) =>
          prev.status != curr.status &&
          curr.status == PermissionHandlerStatus.showingRationale,
      listener: (context, state) async {
        await BiConfirmationDialog.show(
          context: context,
          title: buildRationaleTitle(context, state.requestedPermission!),
          subTitle: buildRationale(context, state.requestedPermission!),
          posButtonTitle: continueButtonText,
          negButtonTitle: cancelButtonText,
          onPosButtonTapped: () {
            BiPermissionRequestHandlerCubit.read(context).onResume();
          },
          onNegButtonTapped: () {
            BiPermissionRequestHandlerCubit.read(context)
                .cancelledRationaleDialog();
          },
        );
      },
      child: child,
    );
  }
}

class _LifecycleListener extends StatefulWidget {
  final Widget child;

  const _LifecycleListener({required this.child});

  @override
  State<StatefulWidget> createState() => _LifecycleListenerState();
}

class _LifecycleListenerState extends State<_LifecycleListener>
    with WidgetsBindingObserver {
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      context.read<BiPermissionRequestHandlerCubit>().onResume();
    } else if (state == AppLifecycleState.inactive) {
      context.read<BiPermissionRequestHandlerCubit>().onInactive();
    }
  }

  @override
  Widget build(BuildContext context) => widget.child;
}

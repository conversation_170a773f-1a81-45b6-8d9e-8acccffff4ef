import 'dart:async';

import 'package:bi_flutter_bluetooth_platform_api/bi_flutter_bluetooth_platform_api.dart';
import 'package:bi_permissions/permissions/permissions.dart';
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:mutex/mutex.dart';

class BiPermissionRequestRepository {
  final _requestMutex = Mutex();

  final StreamController<BiPermission> _requestPermissionStream =
      StreamController.broadcast();

  final List<(BiPermission, Completer<bool>)> _pendingResponses = [];

  Stream<BiPermission> get permissionRequested =>
      _requestPermissionStream.stream;

  final BiSupportedPermissions supportedPermissions;

  BiPermissionRequestRepository({
    required BiBluetoothApi bluetoothApi,
  }) : supportedPermissions =
            BiSupportedPermissions(bluetoothApi: bluetoothApi);

  Future<void> permissionRequestComplete(
    BiPermission permission,
    bool granted,
  ) async {
    await _requestMutex.acquire();
    try {
      final response =
          _pendingResponses.firstWhereOrNull((q) => q.$1 == permission);
      if (response == null) return;

      _pendingResponses.remove(response);
      if (response.$2.isCompleted) {
        return;
      }
      response.$2.complete(granted);

      _requestNextPermissionFromQueue();
    } finally {
      _requestMutex.release();
    }
  }

  void _requestNextPermissionFromQueue() {
    if (_pendingResponses.isNotEmpty) {
      // we have a queue still, so request the next permission
      _requestPermissionStream.add(_pendingResponses.first.$1);
    }
  }

  Future<bool> requestPermission(BiPermission permission) async {
    await _requestMutex.acquire();
    try {
      final response = _pendingResponses
          .firstWhereOrNull((q) => q.$1.type == permission.type);

      // already some version of the request in flight, so return that instead
      // of issuing a new request.
      if (response != null) return response.$2.future;

      if (await permission.granted) return true;

      if (permission.type == BiPermissionType.locationAlways) {
        if (!await supportedPermissions.locationWhenInUse.granted) {
          return false;
        }
      }

      if (await permission.permanentlyDenied &&
          permission.type != BiPermissionType.locationAlways) {
        // iOS will say that location always is perm denied even if it hasn't
        // been requested yet but when in use is granted. So just always assume
        // we need to request it if the permission type is location always.
        return false;
      }

      final completer = Completer<bool>();
      _pendingResponses.add((permission, completer));

      if (_pendingResponses.length > 1) {
        // there is already a queue, so don't add to the request stream yet.
        return completer.future;
      }

      _requestPermissionStream.add(permission);
      return completer.future;
    } finally {
      _requestMutex.release();
    }
  }

  @visibleForTesting
  void close() {
    _requestPermissionStream.close();
    for (final pending in _pendingResponses) {
      if (pending.$2.isCompleted) continue;
      pending.$2.complete(false);
    }
    _pendingResponses.clear();
  }
}

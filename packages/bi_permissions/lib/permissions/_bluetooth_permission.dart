import 'package:bi_flutter_bluetooth_platform_api/plugin/_bi_bluetooth_api.dart';
import 'package:bi_permissions/permissions/_bi_permission.dart';

class BluetoothPermission extends BiPermission {
  final BiBluetoothApi _api;
  const BluetoothPermission(this._api);

  @override
  BiPermissionType get type => BiPermissionType.bluetooth;

  @override
  Future<void> request() async => await _api.requestPermission();

  @override
  Future<bool> get granted async =>
      await _api.permissionStatus() == BluetoothPermissionStatus.granted;

  @override
  Future<bool> get permanentlyDenied async => false;
}

import 'package:equatable/equatable.dart';

enum BiPermissionType {
  unknown,
  bluetooth,
  locationWhenInUse,
  locationAlways,
  notification,
}

abstract class BiPermission extends Equatable {
  BiPermissionType get type;
  Future<void> request();
  Future<bool> get shouldShowRationale => Future.value(false);
  Future<bool> get granted;
  Future<bool> get permanentlyDenied;

  const BiPermission();

  @override
  List<Object?> get props => [type];
}

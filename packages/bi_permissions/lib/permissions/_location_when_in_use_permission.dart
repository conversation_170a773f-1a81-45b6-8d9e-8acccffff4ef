import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationWhenInUsePermission extends BiPermission {
  @override
  BiPermissionType get type => BiPermissionType.locationWhenInUse;

  const LocationWhenInUsePermission();

  @override
  Future<void> request() async => Permission.locationWhenInUse.request();

  @override
  Future<bool> get granted => Permission.locationWhenInUse.isGranted;

  @override
  Future<bool> get permanentlyDenied =>
      Permission.locationWhenInUse.isPermanentlyDenied;
}

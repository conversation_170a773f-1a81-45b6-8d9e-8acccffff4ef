// await Permission.notification.request();

import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationPermission extends BiPermission {
  @override
  BiPermissionType get type => BiPermissionType.notification;

  const NotificationPermission();

  @override
  Future<void> request() async => Permission.notification.request();

  @override
  Future<bool> get shouldShowRationale =>
      Permission.notification.shouldShowRequestRationale;

  @override
  Future<bool> get granted => Permission.notification.isGranted;

  @override
  Future<bool> get permanentlyDenied async =>
      Permission.notification.isPermanentlyDenied;
}

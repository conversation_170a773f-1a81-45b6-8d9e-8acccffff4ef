import 'package:bi_permissions/bi_permissions.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationAlwaysPermission extends BiPermission {
  @override
  BiPermissionType get type => BiPermissionType.locationAlways;

  const LocationAlwaysPermission();

  @override
  Future<void> request() async => Permission.locationAlways.request();

  @override
  Future<bool> get shouldShowRationale =>
      Permission.locationAlways.shouldShowRequestRationale;

  @override
  Future<bool> get granted => Permission.locationAlways.isGranted;

  @override
  Future<bool> get permanentlyDenied async =>
      Permission.locationAlways.isPermanentlyDenied;
}

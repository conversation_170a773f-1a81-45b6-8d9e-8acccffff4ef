import 'package:bi_flutter_bluetooth_platform_api/plugin/plugin.dart';
import 'package:bi_permissions/permissions/permissions.dart';
import 'package:flutter/widgets.dart';

class BiSupportedPermissions {
  final BiBluetoothApi _bluetoothApi;

  BiSupportedPermissions({
    required BiBluetoothApi bluetoothApi,
  }) : _bluetoothApi = bluetoothApi;

  BiPermission get bluetooth => BluetoothPermission(_bluetoothApi);
  BiPermission get locationAlways => const LocationAlwaysPermission();
  BiPermission get locationWhenInUse => const LocationWhenInUsePermission();
  BiPermission get notification => const NotificationPermission();

  @visibleForTesting
  List<BiPermission> get all => [
        bluetooth,
        locationAlways,
        locationWhenInUse,
        notification,
      ];
}

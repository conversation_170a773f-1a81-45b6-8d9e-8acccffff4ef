import 'dart:async';

import 'package:bi_flutter_bloc/bi_flutter_bloc.dart';
import 'package:bi_permissions/cubit/_bi_permission_request_handler_state.dart';
import 'package:bi_permissions/permissions/permissions.dart';
import 'package:bi_permissions/repositories/_bi_permission_request_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BiPermissionRequestHandlerCubit
    extends Cubit<BiPermissionRequestHandlerState> with SubscriptionWatcher {
  final BiPermissionRequestRepository _repo;

  Future<void>? _iOSAppInactivityTimer;

  BiPermissionRequestHandlerCubit(this._repo)
      : super(
          const BiPermissionRequestHandlerState(
            status: PermissionHandlerStatus.idle,
          ),
        ) {
    subscriptions.add(
      _repo.permissionRequested.listen(
        _requestPermission,
      ),
    );
  }

  Future<void> cancelledRationaleDialog() async {
    emit(state.copyWith(status: PermissionHandlerStatus.requesting));
    await onResume();
  }

  Future<void> onInactive() async {
    emit(state.copyWith(appInactive: true));
  }

  Future<void> onResume() async {
    emit(state.copyWith(appInactive: false));

    if (state.status == PermissionHandlerStatus.showingRationale) {
      emit(
        state.copyWith(
          status: PermissionHandlerStatus.requesting,
        ),
      );
      await state.requestedPermission!.request();
      return;
    }

    if (state.status == PermissionHandlerStatus.requesting) {
      await _completeRequest();
    }
  }

  Future<void> _completeRequest() async {
    final permission = state.requestedPermission!;
    emit(
      const BiPermissionRequestHandlerState(
        status: PermissionHandlerStatus.idle,
      ),
    );
    await _repo.permissionRequestComplete(
      permission,
      await permission.granted,
    );
  }

  Future<void> _requestPermission(BiPermission permission) async {
    emit(
      state.copyWith(
        requestedPermission: permission,
        status: PermissionHandlerStatus.requesting,
      ),
    );

    if (await permission.shouldShowRationale) {
      emit(
        state.copyWith(
          status: PermissionHandlerStatus.showingRationale,
        ),
      );
      return;
    }

    if (permission.type == BiPermissionType.locationAlways) {
      _startTimerForLocationAlwaysDialog();
    }

    await permission.request();
  }

  void _startTimerForLocationAlwaysDialog() {
    // iOS specific implementation. Android always fires a hidden intent which triggers
    // the expected inactive -> active app states
    if (defaultTargetPlatform == TargetPlatform.android) return;

    _iOSAppInactivityTimer = Future.delayed(
      const Duration(seconds: 1),
      () async {
        if (state.appInactive ||
            state.status != PermissionHandlerStatus.requesting) {
          // app is currently inactive, indicating the dialog is shown OR
          // the app progressed past the request itself by granting/denying.
          return;
        }

        // app never went inactive, meaning no dialog was shown, so just progress.
        await onResume();
      },
    );
  }

  static BiPermissionRequestHandlerCubit read(BuildContext context) =>
      context.read<BiPermissionRequestHandlerCubit>();

  @override
  Future<void> close() async {
    if (_iOSAppInactivityTimer != null) {
      await _iOSAppInactivityTimer;
    }
    return super.close();
  }
}

import 'package:bi_permissions/permissions/_bi_permission.dart';
import 'package:equatable/equatable.dart';

enum PermissionHandlerStatus {
  requesting,
  showingRationale,
  idle,
}

class BiPermissionRequestHandlerState extends Equatable {
  final PermissionHandlerStatus status;
  final BiPermission? requestedPermission;
  final bool appInactive;

  const BiPermissionRequestHandlerState({
    required this.status,
    this.requestedPermission,
    this.appInactive = false,
  });

  BiPermissionRequestHandlerState copyWith({
    PermissionHandlerStatus? status,
    BiPermission? requestedPermission,
    bool? appInactive,
  }) =>
      BiPermissionRequestHandlerState(
        status: status ?? this.status,
        requestedPermission: requestedPermission ?? this.requestedPermission,
        appInactive: appInactive ?? this.appInactive,
      );

  @override
  List<Object?> get props => [
        status,
        requestedPermission,
        appInactive,
      ];
}

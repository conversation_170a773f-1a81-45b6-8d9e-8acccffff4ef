plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"

    // Google Services plugin
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    compileSdkVersion 36

    compileOptions {
        // flutter_local_notifications requires desugaring
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.biinc.mobile.client"
        minSdkVersion 31
        targetSdkVersion 36
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        // This is not needed for Embark since it does not use OIDC. It's required because we include a package
        // (bi_flutter_core_dio) that depends on OIDC functionality even though Embark does not use it.
        manifestPlaceholders += [
                'appAuthRedirectScheme': '',
        ]
    }

    signingConfigs {
        debug {
            keyAlias 'androiddebugkey'
            keyPassword 'android'
            storeFile file('../debug.keystore')
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    namespace 'com.biinc.mobile.client'

    flavorDimensions += "default"

    productFlavors {
        dev {
            dimension "default"
            // TODO: Uncomment this when the Firebase config is set via code + .env file.
            versionNameSuffix "-dev"
            manifestPlaceholders.appNameSuffix = " (Dev)"
        }
        stage {
            dimension "default"
            versionNameSuffix "-stage"
            manifestPlaceholders.appNameSuffix = " (Stage)"
            ndk {
                // only build for supported phone arch to cut down on cost of deployment.
                abiFilters "arm64-v8a", "armeabi-v7a"
            }
        }
        b4 {
            dimension "default"
            versionNameSuffix "-b4"
            manifestPlaceholders.appNameSuffix = " (B4)"
            ndk {
                // only build for supported phone arch to cut down on cost of deployment.
                abiFilters "arm64-v8a", "armeabi-v7a"
            }
        }
        knox {
            dimension "default"
            versionNameSuffix "-knox"
            applicationId "com.biinc.mobile.client"
            manifestPlaceholders.appNameSuffix = ""
            applicationIdSuffix ".v" + flutterVersionCode
            ndk {
                // only build for the knox supported phone arch to cut down on cost of deployment.
                abiFilters "arm64-v8a"
            }
        }
        prod {
            dimension "default"
            manifestPlaceholders.appNameSuffix = ""
            ndk {
                // only build for supported phone arch to cut down on cost of deployment.
                abiFilters "arm64-v8a", "armeabi-v7a"
            }
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'com.android.support:multidex:1.0.3'
    implementation('com.google.firebase:firebase-messaging:20.2.0') {
        exclude group: 'com.google.android.gms', module: 'play-services-ads-identifier'
    }
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
}
